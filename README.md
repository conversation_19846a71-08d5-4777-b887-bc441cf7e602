# Avinya Ops

**Avinya Ops** provides premium office supplies and equipment to help your business operate efficiently and professionally.

## Project Context

Avinya Ops is a full-stack application designed to streamline procurement and operations for organizations. It enables users to manage office supply requests, approvals, inventory, and organizational data through a modern, user-friendly interface. The project aims to solve common pain points in procurement workflows, such as manual approvals, lack of transparency, and inefficient communication between departments.

**Key Goals:**
- Digitize and automate procurement workflows
- Provide a centralized platform for managing office supplies and equipment
- Enable role-based access and approval hierarchies
- Integrate with modern authentication and storage solutions

**Technologies Used:**
- **Frontend:** React (TypeScript), Vite, PrimeReact, TanStack Router, React Query, Zod, Storybook
- **Backend:** Spring Boot (Java 17+), Spring Data JPA, Hibernate, RESTful APIs, Maven
- **Other:** <PERSON><PERSON> (optional), <PERSON><PERSON>er, ESLint, Jest/Vitest for testing

---

## Project Structure

Below is an overview of the main directories and files in the project, with brief descriptions:

```
avinya-ops/
├── app/                        # Frontend (React + TypeScript)
│   ├── public/                 # Static assets (favicon, SVGs, etc.)
│   ├── src/
│   │   ├── components/         # Reusable UI and form components
│   │   ├── context/            # React Context providers (e.g., Sidebar, Organization)
│   │   ├── data/               # Mock data for development/testing
│   │   ├── formSchemas/        # JSON schemas for dynamic forms
│   │   ├── hooks/              # Custom React hooks (e.g., useAuth, useOrganization)
│   │   ├── layouts/            # Layout components (e.g., MainLayout)
│   │   ├── pages/              # Page-level components (private/public)
│   │   ├── providers/          # App-level providers (e.g., QueryProvider)
│   │   ├── routes/             # Route definitions (TanStack Router)
│   │   ├── styles/             # Global and component CSS (BEM, PrimeFlex)
│   │   ├── types/              # TypeScript types/interfaces
│   │   ├── utils/              # Utility functions (date, enum, etc.)
│   │   └── App.tsx             # Main React app entry
│   ├── .env.example            # Example environment variables
│   ├── .gitignore
│   ├── .prettierrc
│   ├── index.html              # HTML entry point
│   ├── package.json            # Frontend dependencies and scripts
│   └── ...                     # Other config files (eslint, storybook, etc.)
│
├── backend/                    # Backend (Spring Boot + Java)
│   ├── application/            # Main Spring Boot application module
│   │   ├── src/
│   │   │   ├── main/
│   │   │   │   ├── java/com/avinyaops/procurement/ # App config, controllers, etc.
│   │   │   │   └── resources/  # Application configs, templates, logs
│   │   │   └── test/           # Application-level tests
│   │   └── pom.xml             # Maven module config
│   ├── core/                   # Core business logic, domain models, services
│   │   ├── src/
│   │   │   ├── main/java/com/avinyaops/procurement/
│   │   │   └── test/
│   │   └── pom.xml
│   ├── fileupload/             # File upload microservice/module
│   │   ├── src/
│   │   │   ├── main/java/com/avinyaops/procurement/fileupload/
│   │   │   └── test/
│   │   └── pom.xml
│   ├── pom.xml                 # Backend parent Maven config
│   └── ...                     # Additional backend modules/configs
│
├── .gitignore
├── README.md                   # Project documentation (this file)
├── ...                         # Other root-level configs and docs
```

### Directory Descriptions

- **app/**: Contains the entire frontend codebase, including React components, pages, hooks, and configuration for development and production.
- **backend/**: Houses the Spring Boot backend, split into modules for application logic, core domain, and file upload services.
- **formSchemas/**: JSON schemas for dynamic form rendering and validation.
- **types/**: TypeScript type definitions for strong typing across the frontend.
- **routes/**: Centralized route definitions using TanStack Router for code-splitting and nested layouts.
- **styles/**: CSS files using BEM and PrimeFlex conventions for consistent styling.
- **public/**: Static files served directly by the frontend (e.g., images, icons).
- **.env.example**: Template for environment variables required to run the frontend.

---

## Features

- Premium quality office supplies and equipment
- Fast and reliable delivery to your doorstep
- Exceptional customer service and support

---
