import React, { useState, useEffect } from 'react';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { RadioButton } from 'primereact/radiobutton';
import { AutoComplete } from 'primereact/autocomplete';
import { Divider } from 'primereact/divider';
import Button from '@/components/ui/Button/Button';
import CriteriaBuilder from './CriteriaBuilder';
import WorkflowLevels from './WorkflowLevels';
import WorkflowPreview from './WorkflowPreview';
import {
  ApprovalWorkflow,
  WorkflowCriteria,
  CreateApprovalWorkflowRequest,
  WorkflowActionType,
  ApprovalAction
} from '@/types/approvalWorkflow.types';
import { useFormTypes } from '@/hooks/useApprovalWorkflow';
import { useDepartments } from '@/hooks/useDepartment';
import { UserService } from '@/services/api/userService';
import { UserResponse } from '@/types/api/user';
import './ApprovalWorkflowForm.css';
import { useOrganizationContext } from '@/context/OrganizationContext';

interface ApprovalWorkflowFormProps {
  workflow?: ApprovalWorkflow | null;
  onSubmit: (data: CreateApprovalWorkflowRequest) => void;
  onCancel: () => void;
  loading?: boolean;
}

interface WorkflowActionEntry {
  workflowActionType: WorkflowActionType;
  approvalLevels: number;
  selectedDepartmentId: string;
  selectedUserId: number | null;
}

const ApprovalWorkflowForm: React.FC<ApprovalWorkflowFormProps> = ({
  workflow,
  onSubmit,
  onCancel,
  loading = false
}) => {
  const { organizationId } = useOrganizationContext();
  // API hooks
  const { data: formTypes = [], isLoading: formTypesLoading } = useFormTypes(organizationId!);
  const { data: departments = [], isLoading: departmentsLoading } = useDepartments(organizationId!);

  // formData holds top-level fields plus workflowActions array
  const [formData, setFormData] = useState<{
    name: string;
    formId: number;
    criteria: WorkflowCriteria[];
    approvalLevels: number;
    autoApprove: boolean;
    autoReject: boolean;
    actionType: 'approval' | 'auto-approve' | 'auto-reject';
    workflowActions: WorkflowActionEntry[];
  }>({
    name: '',
    formId: 0,
    criteria: [],
    approvalLevels: 1,
    autoApprove: false,
    autoReject: false,
    actionType: 'approval',
    workflowActions: [
      {
        workflowActionType: 'workflow-levels',
        approvalLevels: 1,
        selectedDepartmentId: '',
        selectedUserId: null
      }
    ]
  });

  // errors
  const [errors, setErrors] = useState<Record<string, string>>({});

  // For user AutoComplete: suggestions shared across rows
  const [userSuggestions, setUserSuggestions] = useState<UserResponse[]>([]);
  // Keep selected UserResponse per action index
  const [selectedUsers, setSelectedUsers] = useState<(UserResponse | null)[]>([]);

  // Workflow action type options
  const workflowActionOptions = [
    { label: 'Workflow Levels', value: 'workflow-levels', description: 'Route through multi-level approval hierarchy' },
    { label: 'Department Head', value: 'department-head', description: 'Route to head of selected department' },
    {
      label: 'Department Head of Record Owner',
      value: 'department-head-record-owner',
      description: "Route to head of requester's department"
    },
    { label: 'Employee', value: 'specific-user', description: 'Route to a specific selected employee' }
  ];

  // Department dropdown options
  const departmentOptions = departments.map(dept => ({
    label: dept.name,
    value: dept.id?.toString() || ''
  }));

  //
  // Initialize formData (and selectedUsers) when `workflow` prop changes:
  //
  useEffect(() => {
    if (workflow) {
      // Determine if approval mode
      const isApproval = !workflow.autoApprove && !workflow.autoReject;

      const workflowActions: WorkflowActionEntry[] = [];
      const selUsers: (UserResponse | null)[] = [];

      if (isApproval && workflow.approvals && workflow.approvals.length > 0) {
        // For each saved approval, push an entry
        workflow.approvals.forEach(appr => {
          if (appr.approverType === 'REPORTING_TO') {
            workflowActions.push({
              workflowActionType: 'workflow-levels',
              approvalLevels: appr.approver, // or appr.executionOrder if available
              selectedDepartmentId: '',
              selectedUserId: null
            });
            selUsers.push(null);
          } else if (appr.approverType === 'DEPARTMENT_HEAD') {
            workflowActions.push({
              workflowActionType: 'department-head',
              approvalLevels: 1,
              selectedDepartmentId: appr.approver.toString(),
              selectedUserId: null
            });
            selUsers.push(null);
          } else if (appr.approverType === 'USER') {
            workflowActions.push({
              workflowActionType: 'specific-user',
              approvalLevels: 1,
              selectedDepartmentId: '',
              selectedUserId: appr.approver
            });
            // We can attempt to load the full user object below in another effect if desired
            selUsers.push(null);
          } else if (appr.approverType === 'DEPARTMENT_HEAD_RECORD_OWNER') {
            workflowActions.push({
              workflowActionType: 'department-head-record-owner',
              approvalLevels: 1,
              selectedDepartmentId: '',
              selectedUserId: null
            });
            selUsers.push(null);
          } else {
            // fallback
            workflowActions.push({
              workflowActionType: 'workflow-levels',
              approvalLevels: 1,
              selectedDepartmentId: '',
              selectedUserId: null
            });
            selUsers.push(null);
          }
        });
      } else {
        // No existing approvals or not in approval mode: push one blank
        workflowActions.push({
          workflowActionType: 'workflow-levels',
          approvalLevels: 1,
          selectedDepartmentId: '',
          selectedUserId: null
        });
        selUsers.push(null);
      }

      setFormData(prev => ({
        ...prev,
        name: workflow.name,
        formId: workflow.formId,
        criteria: workflow.criteria,
        approvalLevels: workflow.approvalLevels,
        autoApprove: workflow.autoApprove,
        autoReject: workflow.autoReject,
        actionType: workflow.autoApprove
          ? 'auto-approve'
          : workflow.autoReject
            ? 'auto-reject'
            : 'approval',
        workflowActions
      }));
      setSelectedUsers(selUsers);
    } else {
      // create mode: reset to one blank step
      setFormData(prev => ({
        ...prev,
        name: '',
        formId: 0,
        criteria: [],
        approvalLevels: 1,
        autoApprove: false,
        autoReject: false,
        actionType: 'approval',
        workflowActions: [
          {
            workflowActionType: 'workflow-levels',
            approvalLevels: 1,
            selectedDepartmentId: '',
            selectedUserId: null
          }
        ]
      }));
      setSelectedUsers([null]);
    }
    setErrors({});
  }, [workflow]);

  //
  // If you want to load existing selectedUser objects when editing:
  // For simplicity, here’s a pattern: whenever formData.workflowActions changes,
  // for any action with selectedUserId but no selectedUsers[idx], you could fetch the user:
  //
  useEffect(() => {
    const loadUsersForActions = async () => {
      const newSelUsers = [...selectedUsers];
      let changed = false;
      await Promise.all(
        formData.workflowActions.map(async (action, idx) => {
          if (
            action.workflowActionType === 'specific-user' &&
            action.selectedUserId != null &&
            !newSelUsers[idx]
          ) {
            try {
              // Example: fetch user by ID. If your API has a direct endpoint, use that. Here we do a broad search.
              const users = await UserService.searchUsers(organizationId, '', undefined, undefined);
              const userObj = users.find(u => u.id === action.selectedUserId) || null;
              if (userObj) {
                newSelUsers[idx] = userObj;
                changed = true;
              }
            } catch (err) {
              console.error('Failed to load user for action index', idx, err);
            }
          }
        })
      );
      if (changed) {
        setSelectedUsers(newSelUsers);
      }
    };

    loadUsersForActions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.workflowActions, organizationId]);

  // Validation functions (unchanged except you may want to incorporate per-action validation later)
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    // ... same as before, plus possibly per-action checks ...
    if (!formData.name.trim()) {
      newErrors.name = 'Workflow name is required';
    } else if (formData.name.trim().length < 3) {
      newErrors.name = 'Workflow name must be at least 3 characters long';
    } else if (formData.name.trim().length > 100) {
      newErrors.name = 'Workflow name must be less than 100 characters';
    }
    if (!formData.formId) {
      newErrors.formId = 'Form type is required';
    }
    if (formData.criteria.length === 0) {
      newErrors.criteria = 'At least one criteria is required';
    } else {
      const invalidCriteria = formData.criteria.find(criterion =>
        !criterion.fieldType || !criterion.operator || !criterion.values?.length
      );
      if (invalidCriteria) {
        newErrors.criteria = 'All criteria must have field type, operator, and at least one value selected';
      }
      if (formData.criteria.length > 1) {
        const invalidConnector = formData.criteria.slice(0, -1).find(criterion =>
          !criterion.logicConnector
        );
        if (invalidConnector) {
          newErrors.criteria = 'Logic connectors (AND/OR) are required between criteria';
        }
      }
    }
    if (formData.autoApprove && formData.autoReject) {
      newErrors.autoActions = 'Cannot enable both auto-approve and auto-reject';
    }
    if (formData.actionType === 'approval' && formData.approvalLevels < 1) {
      newErrors.approvalLevels = 'At least one approval level is required';
    }
    // Per-action validation example:
    formData.workflowActions.forEach((action, idx) => {
      if (formData.actionType === 'approval') {
        if (action.workflowActionType === 'department-head') {
          if (!action.selectedDepartmentId) {
            newErrors[`workflowActions.${idx}.selectedDepartmentId`] = 'Please select a department';
          } else {
            const selDept = departments.find(d => d.id?.toString() === action.selectedDepartmentId);
            if (!selDept?.departmentHeadId) {
              newErrors[`workflowActions.${idx}.selectedDepartmentId`] =
                'Selected department does not have a department head assigned';
            }
          }
        }
        if (action.workflowActionType === 'specific-user') {
          if (!action.selectedUserId) {
            newErrors[`workflowActions.${idx}.selectedUserId`] = 'Please select an employee';
          }
        }
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isFormValid = (): boolean => {
    // Simplified: reuse validateForm or replicate checks without setting state
    return Boolean(
      formData.name.trim().length >= 3 &&
      formData.formId &&
      formData.criteria.length > 0 &&
      formData.criteria.every(criterion =>
        criterion.fieldType && criterion.operator && criterion.values?.length > 0
      ) &&
      (formData.criteria.length === 1 ||
        formData.criteria.slice(0, -1).every(criterion => criterion.logicConnector)) &&
      !(formData.autoApprove && formData.autoReject) &&
      (formData.actionType !== 'approval' || formData.approvalLevels >= 1)
    );
  };

  // Transform for submission
  const transformFormDataForSubmission = (): CreateApprovalWorkflowRequest => {
    const approvals: ApprovalAction[] = [];
    if (formData.actionType === 'approval') {
      formData.workflowActions.forEach(action => {
        switch (action.workflowActionType) {
          case 'workflow-levels':
            approvals.push({
              approver: action.approvalLevels,
              approverType: 'REPORTING_TO'
            });
            break;
          case 'department-head': {
            const dept = departments.find(d => d.id?.toString() === action.selectedDepartmentId);
            const headId = dept?.departmentHeadId ?? parseInt(action.selectedDepartmentId, 10);
            approvals.push({
              approver: headId,
              approverType: 'DEPARTMENT_HEAD'
            });
            break;
          }
          case 'department-head-record-owner':
            approvals.push({
              approver: 1, // or however you derive
              approverType: 'DEPARTMENT_HEAD_RECORD_OWNER'
            });
            break;
          case 'specific-user':
            if (action.selectedUserId != null) {
              approvals.push({
                approver: action.selectedUserId,
                approverType: 'USER'
              });
            }
            break;
          default:
            break;
        }
      });
    }
    return {
      name: formData.name,
      formId: formData.formId,
      criteria: formData.criteria,
      approvalLevels: formData.approvalLevels,
      autoApprove: formData.autoApprove,
      autoReject: formData.autoReject,
      approvals
    };
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      const submissionData = transformFormDataForSubmission();
      onSubmit(submissionData);
    }
  };

  // Generic top-level input change
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    if (errors[field]) {
      setErrors(prev => {
        const cp = { ...prev };
        delete cp[field];
        return cp;
      });
    }
  };

  // Criteria change
  const handleCriteriaChange = (criteria: WorkflowCriteria[]) => {
    handleInputChange('criteria', criteria);
    if (criteria.length > 0 &&
      criteria.every(criterion => criterion.fieldType && criterion.operator && criterion.values?.length > 0) &&
      (criteria.length === 1 || criteria.slice(0, -1).every(criterion => criterion.logicConnector))) {
      if (errors.criteria) {
        setErrors(prev => {
          const cp = { ...prev };
          delete cp.criteria;
          return cp;
        });
      }
    }
  };

  // ActionType change
  const handleActionTypeChange = (actionType: 'approval' | 'auto-approve' | 'auto-reject') => {
    setFormData(prev => ({
      ...prev,
      actionType,
      autoApprove: actionType === 'auto-approve',
      autoReject: actionType === 'auto-reject',
      approvalLevels: actionType === 'approval' ? prev.approvalLevels : 1,
      // If switching out of approval, reset workflowActions to single blank step
      workflowActions:
        actionType === 'approval'
          ? prev.workflowActions
          : [
            {
              workflowActionType: 'workflow-levels',
              approvalLevels: 1,
              selectedDepartmentId: '',
              selectedUserId: null
            }
          ]
    }));
    if (actionType !== 'approval') {
      // reset selectedUsers
      setSelectedUsers([null]);
    }
    if (errors.autoActions) {
      setErrors(prev => {
        const cp = { ...prev };
        delete cp.autoActions;
        return cp;
      });
    }
  };

  //
  // Per-action handlers (index-based):
  //
  const handleWorkflowActionTypeChangeAt = (index: number, workflowActionType: WorkflowActionType) => {
    setFormData(prev => {
      const newActions = prev.workflowActions.map((act, idx) => {
        if (idx === index) {
          return {
            workflowActionType,
            // reset other fields on change of type:
            approvalLevels: workflowActionType === 'workflow-levels' ? act.approvalLevels : 1,
            selectedDepartmentId: workflowActionType === 'department-head' ? act.selectedDepartmentId : '',
            selectedUserId: workflowActionType === 'specific-user' ? act.selectedUserId : null
          };
        }
        return act;
      });
      return {
        ...prev,
        workflowActions: newActions
      };
    });
    // Reset selectedUsers entry if needed:
    setSelectedUsers(prevSel => {
      const cp = [...prevSel];
      if (workflowActionType !== 'specific-user') {
        cp[index] = null;
      }
      return cp;
    });
    // Clear any per-action errors:
    setErrors(prev => {
      const cp = { ...prev };
      delete cp[`workflowActions.${index}.selectedDepartmentId`];
      delete cp[`workflowActions.${index}.selectedUserId`];
      return cp;
    });
  };

  const handleApprovalLevelsChangeAt = (index: number, levels: number) => {
    setFormData(prev => {
      const newActions = prev.workflowActions.map((act, idx) => {
        if (idx === index) {
          return { ...act, approvalLevels: levels };
        }
        return act;
      });
      return { ...prev, workflowActions: newActions, approvalLevels: levels };
    });
    // Clear related error if any:
    setErrors(prev => {
      const cp = { ...prev };
      delete cp[`workflowActions.${index}.approvalLevels`];
      return cp;
    });
  };

  const handleDepartmentChangeAt = (index: number, departmentId: string) => {
    setFormData(prev => {
      const newActions = prev.workflowActions.map((act, idx) => {
        if (idx === index) {
          return { ...act, selectedDepartmentId: departmentId };
        }
        return act;
      });
      return { ...prev, workflowActions: newActions };
    });
    setErrors(prev => {
      const cp = { ...prev };
      delete cp[`workflowActions.${index}.selectedDepartmentId`];
      return cp;
    });
  };

  const handleUserSearch = async (query: string) => {
    if (!query || query.length < 2) {
      setUserSuggestions([]);
      return;
    }
    try {
      const users = await UserService.searchUsers(organizationId, query, undefined, undefined);
      setUserSuggestions(users);
    } catch (error) {
      console.error('Error searching users:', error);
      setUserSuggestions([]);
    }
  };

  const handleUserChangeAt = (index: number, user: UserResponse | null) => {
    // Update selectedUsers array
    setSelectedUsers(prev => {
      const cp = [...prev];
      cp[index] = user;
      return cp;
    });
    // Update formData.workflowActions[].selectedUserId
    setFormData(prev => {
      const newActions = prev.workflowActions.map((act, idx) => {
        if (idx === index) {
          return { ...act, selectedUserId: user?.id ?? null };
        }
        return act;
      });
      return { ...prev, workflowActions: newActions };
    });
    setErrors(prev => {
      const cp = { ...prev };
      delete cp[`workflowActions.${index}.selectedUserId`];
      return cp;
    });
  };

  // Add a new blank action
  const handleAddAction = () => {
    setFormData(prev => ({
      ...prev,
      workflowActions: [
        ...prev.workflowActions,
        {
          workflowActionType: 'workflow-levels',
          approvalLevels: 1,
          selectedDepartmentId: '',
          selectedUserId: null
        }
      ]
    }));
    setSelectedUsers(prev => [...prev, null]);
  };

  // Remove an action at index
  const handleRemoveAction = (index: number) => {
    setFormData(prev => {
      const newActions = prev.workflowActions.filter((_, idx) => idx !== index);
      return { ...prev, workflowActions: newActions };
    });
    setSelectedUsers(prev => prev.filter((_, idx) => idx !== index));
  };

  // Custom item template for user dropdown
  const userItemTemplate = (user: UserResponse) => {
    return (
      <div className="flex align-items-center">
        <i className="pi pi-user mr-2"></i>
        <div>
          <div className="font-medium">{user.name}</div>
          <div className="text-sm text-color-secondary">{user.email}</div>
        </div>
      </div>
    );
  };

  return (
    <div className="approval-workflow-form">
      <form onSubmit={handleSubmit}>
        {/* Basic Information Section */}
        <div className="form-section">
          <h3 className="section-title">Basic Information</h3>
          <div className="form-grid">
            <div className="form-field">
              <label htmlFor="workflow-name" className="field-label">
                Workflow Name <span className="required">*</span>
              </label>
              <InputText
                id="workflow-name"
                value={formData.name}
                onChange={e => handleInputChange('name', e.target.value)}
                placeholder="Enter workflow name"
                className={`w-full ${errors.name ? 'p-invalid' : ''}`}
              />
              {errors.name && <small className="p-error">{errors.name}</small>}
            </div>
            <div className="form-field">
              <label htmlFor="form-type" className="field-label">
                Form Type <span className="required">*</span>
              </label>
              <Dropdown
                id="form-type"
                value={formData.formId}
                options={formTypes}
                onChange={e => handleInputChange('formId', e.value)}
                placeholder="Select form type"
                className={`w-full ${errors.formId ? 'p-invalid' : ''}`}
                filter
                loading={formTypesLoading}
              />
              {errors.formId && <small className="p-error">{errors.formId}</small>}
            </div>
          </div>
        </div>

        <Divider />

        {/* Criteria Section */}
        <div className="form-section">
          <h3 className="section-title">Criteria Configuration</h3>
          <p className="section-description">
            Define the conditions that will trigger this approval workflow.
          </p>

          <CriteriaBuilder
            criteria={formData.criteria}
            onChange={handleCriteriaChange}
            error={errors.criteria}
          />
        </div>

        <Divider />

        {/* Action Type Selection */}
        <div className="form-section">
          <h3 className="section-title">Workflow Action</h3>
          <p className="section-description">
            Choose how requests matching the criteria should be handled.
          </p>
          <div className="action-type-selection">
            {/* Approval Workflow Option */}
            <div className="action-option">
              <div className="action-radio">
                <RadioButton
                  inputId="action-approval"
                  value="approval"
                  checked={formData.actionType === 'approval'}
                  onChange={e => handleActionTypeChange(e.value)}
                />
                <label htmlFor="action-approval" className="action-label">
                  Approval Workflow
                </label>
              </div>
              <div className="action-description">
                Route requests through a multi-level approval process
              </div>

              {/* Show workflow action options when approval is selected */}
              {formData.actionType === 'approval' &&
                formData.workflowActions.map((action, idx) => (
                  <div key={idx} className="workflow-action-container border p-3 mb-3 rounded">
                    <div className="mt-3 flex flex-wrap gap-4 items-center">
                      <div className="flex-1 min-w-[250px]">
                        <label htmlFor={`workflow-action-type-${idx}`} className="field-label">
                          Workflow Action Type <span className="required">*</span>
                        </label>
                        <Dropdown
                          id={`workflow-action-type-${idx}`}
                          value={action.workflowActionType}
                          options={workflowActionOptions}
                          onChange={e =>
                            handleWorkflowActionTypeChangeAt(idx, e.value)
                          }
                          placeholder="Select workflow action type"
                          className="w-full"
                          optionLabel="label"
                          optionValue="value"
                        />
                        <small className="field-description">
                          {workflowActionOptions.find(opt => opt.value === action.workflowActionType)
                            ?.description}
                        </small>
                      </div>

                      {/* Show workflow levels for workflow-levels action type */}
                      {action.workflowActionType === 'workflow-levels' && (
                        <div className="flex-1 min-w-[450px]">
                          <WorkflowLevels
                            levels={action.approvalLevels}
                            onChange={val => handleApprovalLevelsChangeAt(idx, val)}
                          />
                          {errors[`workflowActions.${idx}.approvalLevels`] && (
                            <small className="p-error">
                              {errors[`workflowActions.${idx}.approvalLevels`]}
                            </small>
                          )}
                        </div>
                      )}

                      {action.workflowActionType === 'department-head' && (
                        <div className="flex-1 min-w-[250px]">
                          <label htmlFor={`department-selection-${idx}`} className="field-label">
                            Select Department <span className="required">*</span>
                          </label>
                          <Dropdown
                            id={`department-selection-${idx}`}
                            value={action.selectedDepartmentId}
                            options={departmentOptions}
                            onChange={e => handleDepartmentChangeAt(idx, e.value)}
                            placeholder="Select department"
                            className={`w-full ${
                              errors[`workflowActions.${idx}.selectedDepartmentId`]
                                ? 'p-invalid'
                                : ''
                            }`}
                            optionLabel="label"
                            optionValue="value"
                            filter
                            loading={departmentsLoading}
                          />
                          {errors[`workflowActions.${idx}.selectedDepartmentId`] && (
                            <small className="p-error">
                              {errors[`workflowActions.${idx}.selectedDepartmentId`]}
                            </small>
                          )}
                          <small className="field-description">
                            Requests will be routed to the head of the selected department
                          </small>
                        </div>
                      )}

                      {action.workflowActionType === 'specific-user' && (
                        <div className="flex-1 min-w-[250px]">
                          <label htmlFor={`user-selection-${idx}`} className="field-label">
                            Select User <span className="required">*</span>
                          </label>
                          <AutoComplete
                            id={`user-selection-${idx}`}
                            value={selectedUsers[idx] || undefined}
                            suggestions={userSuggestions}
                            completeMethod={e => handleUserSearch(e.query)}
                            onChange={e => handleUserChangeAt(idx, e.value)}
                            placeholder="Search for user"
                            className={`w-full ${
                              errors[`workflowActions.${idx}.selectedUserId`] ? 'p-invalid' : ''
                            }`}
                            itemTemplate={userItemTemplate}
                            field="name"
                            forceSelection={true}
                            emptyMessage="No users found"
                          />
                          {errors[`workflowActions.${idx}.selectedUserId`] && (
                            <small className="p-error">
                              {errors[`workflowActions.${idx}.selectedUserId`]}
                            </small>
                          )}
                          <small className="field-description">
                            Requests will be routed to the selected user for approval
                          </small>
                        </div>
                      )}
                        <div className="flex">
                        <Button
                          type = "button"
                          variant="outline" 
                          onClick={handleAddAction}
                          icon = "pi pi-plus"
                        ></Button>

                        {idx === formData.workflowActions.length - 1 && (
                          <Button
                            type = "button"
                            variant="outline"
                            disabled={idx==0}
                            onClick={() => handleRemoveAction(idx)}
                            icon = "pi pi-trash"
                          >
                          </Button>
                        )}
                      </div>
                    </div>

                    {action.workflowActionType === 'department-head-record-owner' && (
                      <div className="record-owner-description mt-3">
                        <div className="info-box">
                          <i className="pi pi-info-circle"></i>
                          <span>
                            Requests will be automatically routed to the department head of
                            the person who initiated the request. No additional configuration
                            is required.
                          </span>
                        </div>
                      </div>
                    )}

                    
                  </div>
                ))}
            </div>

            {/* Auto-approve Option */}
            <div className="action-option">
              <div className="action-radio">
                <RadioButton
                  inputId="action-auto-approve"
                  value="auto-approve"
                  checked={formData.actionType === 'auto-approve'}
                  onChange={e => handleActionTypeChange(e.value)}
                />
                <label htmlFor="action-auto-approve" className="action-label">
                  Auto-approve
                </label>
              </div>
              <div className="action-description">
                Automatically approve requests matching criteria
              </div>
            </div>

            {/* Auto-reject Option */}
            <div className="action-option">
              <div className="action-radio">
                <RadioButton
                  inputId="action-auto-reject"
                  value="auto-reject"
                  checked={formData.actionType === 'auto-reject'}
                  onChange={e => handleActionTypeChange(e.value)}
                />
                <label htmlFor="action-auto-reject" className="action-label">
                  Auto-reject
                </label>
              </div>
              <div className="action-description">
                Automatically reject requests matching criteria
              </div>
            </div>
          </div>
          {errors.autoActions && <small className="p-error">{errors.autoActions}</small>}
        </div>

        <Divider />

        {/* Workflow Preview Section */}
        <div className="form-section">
          <WorkflowPreview
            criteria={formData.criteria}
            approvalLevels={formData.approvalLevels}
            autoApprove={formData.autoApprove}
            autoReject={formData.autoReject}
            // For preview, you might pick the first action or aggregate; adapt as needed
            workflowActionType={formData.workflowActions[0]?.workflowActionType}
            selectedDepartmentId={formData.workflowActions[0]?.selectedDepartmentId || ''}
            selectedDepartmentName={
              departmentOptions.find(dept => dept.value === formData.workflowActions[0]?.selectedDepartmentId)
                ?.label
            }
            selectedUserId={formData.workflowActions[0]?.selectedUserId ?? undefined}
            selectedUserName={selectedUsers[0]?.name}
          />
        </div>

        {/* Form Actions */}
        <div className="form-actions">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            isLoading={loading}
            disabled={loading || !isFormValid()}
          >
            {workflow ? 'Update Workflow' : 'Create Workflow'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ApprovalWorkflowForm;
