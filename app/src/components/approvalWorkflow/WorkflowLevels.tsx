import React from 'react';
// import { RadioButton } from 'primereact/radiobutton';
import { Dropdown } from 'primereact/dropdown';
import { approvalLevelOptions } from '@/data/mockApprovalData';
import './WorkflowLevels.css';

interface WorkflowLevelsProps {
  levels: number;
  onChange: (levels: number) => void;
}

const WorkflowLevels: React.FC<WorkflowLevelsProps> = ({
  levels,
  onChange
}) => {
  // Get description for approval level
  const getLevelDescription = (level: number): string => {
    switch (level) {
      case 1:
        return 'Request goes directly to the employee\'s reporting manager for approval.';
      case 2:
        return 'Request goes to the employee\'s reporting manager, then to their manager\'s reporting manager.';
      case 3:
        return 'Request follows a 3-level hierarchy: employee → direct manager → manager\'s manager → manager\'s manager\'s manager.';
      case 4:
        return 'Request follows a 4-level hierarchy through the complete reporting chain.';
      default:
        return 'Custom approval hierarchy.';
    }
  };

  // Get approval path visualization
  const getApprovalPath = (level: number): string[] => {
    const basePath = ['Employee submits request'];
    
    for (let i = 1; i <= level; i++) {
      if (i === 1) {
        basePath.push('Direct Reporting Manager');
      } else {
        basePath.push(`Level ${i} Manager`);
      }
    }
    
    basePath.push('Request Approved/Rejected');
    return basePath;
  };

  return (
    <div className="workflow-levels">
      {/* Level Selection */}
      <div className="level-selection"> 
       
      <label htmlFor="approval-levels-dropdown" className="field-label">
        Select Approval Level
     </label>
     <Dropdown
            id="approval-levels-dropdown"
        value={levels}
        options={approvalLevelOptions}
        onChange={(e) => onChange(e.value)}
        optionLabel="label"
        optionValue="value"
        placeholder="Choose level"
        className="w-full"
      />
          
        
      </div>

      {/* Approval Path Visualization */}
      

      {/* Additional Information */}
      
    </div>
  );
};

export default WorkflowLevels;
