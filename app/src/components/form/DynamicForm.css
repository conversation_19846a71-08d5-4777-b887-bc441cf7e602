@import url(../../variables.css);

.dynamic-form {
  width: 100%;
  max-width: 100%;
}

.form-fields-container {
  display: flex;
  flex-wrap: wrap;
  margin: -0.75rem; /* Negative margin to offset the padding of field wrappers */
}

.form-field-wrapper {
  flex: 1 0 100%; /* Full width by default (mobile) */
  padding: 0.5rem 0.75rem; /* Reduced vertical padding */
  box-sizing: border-box;
}

/* Checkbox fields should always be full width for better alignment */
.form-field-wrapper .field input[type="checkbox"] {
  margin-right: 0.5rem;
}

/* Ensure all form fields have consistent width and spacing */
.form-field-wrapper .field,
.form-field-wrapper .field-checkbox {
  margin-bottom: 0 !important; /* Remove default margin as we're using the wrapper padding */
}

/* Override the mb-3 class that's applied to form fields */
/* .form-field-wrapper .mb-3,
.form-field-wrapper .mb-2 {
  margin-bottom: 0 !important;
} */

.form-field-wrapper .field label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: var(--font-medium);
  min-height: 1.25rem; /* Slightly reduced height for labels */
}

.form-field-wrapper .field .p-inputtext,
.form-field-wrapper .field .p-dropdown,
.form-field-wrapper .field .p-calendar,
.form-field-wrapper .field .p-inputgroup {
  width: 100%;
  min-height: 2.5rem; /* Ensure consistent height for input fields */
}

/* Ensure consistent styling for error messages */
.form-field-wrapper .field .p-error {
  display: block;
  margin-top: 0.25rem;
  min-height: 0; /* Remove minimum height when no error */
}

.form-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-top: 0;
  padding: 0;
  justify-content: flex-end;
}

/* Add margin between buttons for better spacing */
.form-actions .button {
  margin-top: 0.5rem;
}

/* Media query for tablets and larger screens */
@media screen and (min-width: 768px) {
  .form-field-wrapper {
    flex: 0 0 50%; /* Two columns on tablet and larger screens */
  }

  /* Checkbox fields should remain full width */
  .form-field-wrapper:has(input[type="checkbox"]) {
    flex: 0 0 100%;
  }

  /* Fallback for browsers that don't support :has() */
  .form-field-wrapper.checkbox-field {
    flex: 0 0 100%;
  }
}

/* Media query for larger screens */
@media screen and (min-width: 1200px) {
  .form-fields-container {
    margin: -0.75rem; /* Keep consistent with the padding */
  }

  .form-field-wrapper {
    padding: 0.5rem 0.75rem; /* Keep consistent with mobile padding */
  }
}
