import React from 'react';
import { useFormContext, useField<PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import Button from '@/components/ui/Button/Button';
import { InputText } from 'primereact/inputtext';
// import './DynamicKeyValueField.css';

interface DynamicKeyValueFieldProps {
  name: string;
  label?: string;
}

export const DynamicKeyValueField: React.FC<DynamicKeyValueFieldProps> = ({ name, label }) => {
  const { control, setValue, watch } = useFormContext();
  const { fields, append, remove } = useFieldArray({
    control,
    name: name as any,
  });

  return (
    <div className="dynamic-keyvalue-field">
      {label && <label className="block mb-2 font-semibold">{label}</label>}
      <div className="keyvalue-list">
        {fields.map((field, idx) => (
          <div className="keyvalue-row flex gap-2 mb-3" key={field.id}>
            <Controller
              name={`${name}.${idx}.key`}
              control={control}
              defaultValue={(field as { key?: string }).key || ''}
              render={({ field }) => (
                <InputText
                  {...field}
                  className="keyvalue-input"
                  placeholder="Key"

                />
              )}
            />
            <Controller
              name={`${name}.${idx}.value`}
              control={control}
              defaultValue={(field as { value?: string }).value || ''}
              render={({ field }) => (
                <InputText
                  {...field}
                  className="keyvalue-input"
                  placeholder="Value"
                />
              )}
            />
            <Button
              type="button"
              variant="danger"
              size="small"
              onClick={() => remove(idx)}
              className="keyvalue-remove"
            >
              &nbsp;-&nbsp;
            </Button>
          </div>
        ))}
        <Button
          type="button"
          variant="outline"
          size="small"
          onClick={() => append({ key: '', value: '' })}
          className="keyvalue-add"
        >
          + Add Attribute
        </Button>
      </div>
    </div>
  );
}; 