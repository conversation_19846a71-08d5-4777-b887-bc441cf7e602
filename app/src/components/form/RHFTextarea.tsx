import { Controller, useFormContext } from 'react-hook-form';
import { InputTextarea } from 'primereact/inputtextarea';

interface RHFTextareaProps {
  name: string;
  label?: string;
  placeholder?: string;
  icon?: string;
  rows?: number;
  cols?: number;
  autoResize?: boolean;
  maxLength?: number;
  rules?: any;
}

export const RHFTextarea = ({
  name,
  label,
  placeholder,
  icon,
  rows = 3,
  cols,
  autoResize = true,
  maxLength,
  rules,
  ...rest
}: RHFTextareaProps) => {
  const {
    control,
    formState: { errors },
  } = useFormContext();

  const error = errors[name]?.message as string | undefined;

  return (
    <div className="field mb-3">
      {label && <label htmlFor={name} className="block mb-1">{label}</label>}
      <Controller
        name={name}
        control={control}
        rules={rules}
        render={({ field }) => (
          <div className={`p-inputgroup ${error ? 'p-invalid' : ''}`}>
            {icon && <span className="p-inputgroup-addon"><i className={`pi ${icon}`}></i></span>}
            <InputTextarea
              id={name}
              value={field.value || ''}
              onChange={(e) => field.onChange(e.target.value)}
              onBlur={field.onBlur}
              placeholder={placeholder}
              rows={rows}
              cols={cols}
              autoResize={autoResize}
              maxLength={maxLength}
              className={`w-full ${error ? 'p-invalid' : ''}`}
              {...rest}
            />
          </div>
        )}
      />
      {error && <small className="p-error">{error}</small>}
    </div>
  );
};
