.card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    width: 100%;
    transition: all 0.3s ease;
}

/* Card Variants */
.card--default {
    box-shadow: var(--card-shadow);
}

.card--elevated {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
}

.card--outlined {
    border: 1px solid var(--border-primary);
    box-shadow: var(--card-shadow);
}

.card--flat {
    box-shadow: 0;
}

/* Card Padding Variants */
.card--padding-none {
    padding: 0;
}

.card--padding-small .card__content {
    padding: 0.75rem;
}

.card--padding-medium .card__content {
    padding: 1.25rem;
}

.card--padding-large .card__content {
    padding: var(--card-padding);
}

/* Card Header */
.card__header {
    padding: var(--card-header-padding);
    border-bottom: 1px solid var(--border-primary);
}

.card__title {
    font-size: var(--text-2xl);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
}

.card__subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0.25rem 0 0;
}

/* Card Content */
.card__content {
    color: var(--text-primary);
}

/* Card Footer */
.card__footer {
    padding: 0.75rem 1.25rem;
    border-top: 1px solid var(--border-primary);
    background: var(--bg-secondary);
}

/* Interactive States */
.card--hoverable:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08);
}

.card--clickable {
    cursor: pointer;
}

.card--clickable:active {
    transform: translateY(0);
    box-shadow: var(--card-shadow);
}

/* Responsive Design */
@media (max-width: 768px) {
    .card--padding-medium .card__content {
        padding: 1rem;
    }

    .card--padding-large .card__content {
        padding: var(--card-padding-mobile);
    }

    .card__header {
        padding: var(--card-header-padding-mobile);
    }

    .card__footer {
        padding: 0.5rem 1rem;
    }
}