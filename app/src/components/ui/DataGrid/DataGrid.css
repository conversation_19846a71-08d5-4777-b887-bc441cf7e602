.data-grid-container {
    width: 100%;
}

.data-grid-container .p-datatable {
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* Table styles */
.data-grid-container .p-datatable .p-datatable-header {
    background: var(--surface-section);
    border: 1px solid var(--surface-border);
    border-bottom: 0;
    padding: 1rem;
}

.data-grid-container .p-datatable .p-datatable-thead > tr > th,
.data-grid-container .p-datatable .p-datatable-tbody > tr > td {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--surface-border);
    font-size: 0.875rem;
}

.data-grid-container .p-datatable .p-datatable-thead > tr > th {
    background: var(--surface-section);
    color: var(--text-color);
    font-weight: 600;
}

/* Hover state */
.data-grid-container .p-datatable .p-datatable-tbody > tr:hover {
    background: var(--surface-hover);
}

/* Pagination */
.data-grid-container .p-paginator {
    background: var(--surface-section);
    border: 1px solid var(--surface-border);
    border-top: 0;
    padding: 0.15rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.data-grid-container .p-paginator .p-paginator-pages .p-paginator-page {
    min-width: 1.5rem;
    height: 1.5rem;
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.data-grid-container .p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
    background: var(--primary-color);
    border-radius: var(--border-radius);
    color: var(--surface-section);
}

.data-grid-container .p-paginator .p-dropdown {
    height: 2.5rem;
    min-width: 3rem;
}

.data-grid-container .p-paginator .p-dropdown .p-dropdown-label {
    padding: 0.15rem 0.5rem;
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

/* States */
.data-grid-container .p-datatable-emptymessage {
    padding: 2rem;
    text-align: center;
    color: var(--text-color-secondary);
}

/* Sort icon */
.data-grid-container .p-datatable .p-sortable-column-icon {
    color: var(--text-color-secondary) !important;
    margin-left: 0.5rem;
}

@media screen and (max-width: 768px) {
    .data-grid-container .p-datatable .p-datatable-thead > tr > th,
    .data-grid-container .p-datatable .p-datatable-tbody > tr > td {
        padding: 0.75rem;
    }
}