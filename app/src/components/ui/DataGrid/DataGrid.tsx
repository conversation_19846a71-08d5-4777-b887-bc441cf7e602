import React, { useState } from 'react';
import { DataTable, DataTableStateEvent } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Paginator } from 'primereact/paginator';
import './DataGrid.css';

export interface ColumnConfig {
    field: string;
    header: string;
    sortable?: boolean;
    body?: (rowData: any) => React.ReactNode;
    style?: React.CSSProperties;
    className?: string;
}

export interface DataGridProps {
    value: any[];
    columns: ColumnConfig[];
    totalRecords: number;
    loading?: boolean;
    lazy?: boolean;
    onPage?: (event: { first: number; rows: number; page: number }) => void;
    onSort?: (event: DataTableStateEvent) => void;
    sortField?: string;
    sortOrder?: number;
    className?: string;
    emptyMessage?: string;
    paginator?: boolean;
    rows?: number;
    rowsPerPageOptions?: number[];
    showGridLines?: boolean;
    stripedRows?: boolean;
    size?: 'small' | 'normal' | 'large';
    selection?: any;
    selectionMode?: 'single' | 'multiple' | 'checkbox' | 'radiobutton';
    onSelectionChange?: (e: { value: any }) => void;
    dragSelection?: boolean;
}

export const DataGrid: React.FC<DataGridProps> = ({
    value,
    columns,
    totalRecords,
    loading = false,
    lazy = true,
    onPage,
    onSort,
    sortField,
    sortOrder,
    className = '',
    emptyMessage = 'No records found',
    paginator = true,
    rows = 10,
    rowsPerPageOptions = [10, 25, 50],
    showGridLines = true,
    stripedRows = true,
    size = 'normal',
    selection,
    selectionMode,
    onSelectionChange,
    dragSelection = selectionMode === 'multiple' || selectionMode === 'checkbox',
    ...rest
}) => {
    const [first, setFirst] = useState(0);

    const handlePageChange = (event: { first: number; rows: number; page: number }) => {
        setFirst(event.first);
        onPage?.(event);
    };

    const handleSort = (event: DataTableStateEvent) => {
        onSort?.(event);
    };

    const getSizeClass = () => {
        switch (size) {
            case 'small':
                return 'p-datatable-sm';
            case 'large':
                return 'p-datatable-lg';
            default:
                return '';
        }
    };

    // Create a function to determine if we need to add a selection column
    const needsSelectionColumn = selectionMode === 'checkbox' || selectionMode === 'radiobutton';

    // Determine the internal selection mode for the selection column
    const getSelectionColumnMode = () => {
        if (selectionMode === 'checkbox') return 'multiple';
        if (selectionMode === 'radiobutton') return 'single';
        return undefined;
    };

    const getSelectionRowMode = () => {
        if (selectionMode === 'checkbox') return null;
        if (selectionMode === 'radiobutton') return null;
        return selectionMode;
    };

    return (
        <div className={`data-grid-container ${className}`}>
            <DataTable
                value={value}
                lazy={lazy}
                loading={loading}
                first={first}
                rows={rows}
                totalRecords={totalRecords}
                onSort={handleSort}
                sortMode="single"
                sortField={sortField}
                sortOrder={sortOrder}
                emptyMessage={emptyMessage}
                showGridlines={showGridLines}
                stripedRows={stripedRows}
                className={getSizeClass()}
                selection={selection}
                selectionMode={getSelectionRowMode() as any}
                onSelectionChange={onSelectionChange}
                dragSelection={dragSelection}
                {...rest}
            >
                {needsSelectionColumn && (
                    <Column
                        selectionMode={getSelectionColumnMode()}
                        headerStyle={{ width: '3rem' }}
                    />
                )}
                {columns.map((col) => (
                    <Column
                        key={col.field}
                        field={col.field}
                        header={col.header}
                        sortable={col.sortable}
                        body={col.body}
                        style={col.style}
                        className={col.className}
                    />
                ))}
            </DataTable>
            {/* {paginator && (
                <Paginator
                    first={first}
                    rows={rows}
                    totalRecords={totalRecords}
                    rowsPerPageOptions={rowsPerPageOptions}
                    onPageChange={handlePageChange}
                    className="mt-3 data-grid-paginator"
                />
            )} */}
        </div>
    );
};
