import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useLocation } from '@tanstack/react-router';
import { sidebarConfig, SidebarConfigItem } from '@/config/sidebarConfig';
import { useSidebar } from '@/hooks/useSidebar';
import { useAuth } from '@/hooks/useAuth';
import { Role, RoleLabels } from '@/types/api/user';
import './SidebarV2.css';

export const APP_NAME = "Avinya Ops";

const SidebarV2: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isOpen, closeSidebar } = useSidebar();
  const { user, logout } = useAuth();
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({});
  const [showPopover, setShowPopover] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);
  
  // Check if a route is active
  const isRouteActive = (path: string | undefined): boolean => {
    if (!path) return false;
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };
  
  // Initialize expandedItems based on active routes
  useEffect(() => {
    const newExpandedItems: Record<string, boolean> = {};
    
    // Recursively check items to find which ones should be expanded
    const checkItems = (items: SidebarConfigItem[]) => {
      items.forEach(item => {
        if (item.items && item.items.length > 0) {
          // Check if any child is active
          const hasActiveChild = item.items.some(child => 
            isRouteActive(child.to) || 
            (child.items && child.items.some(grandChild => isRouteActive(grandChild.to)))
          );
          
          // If this item has an active child, mark it as expanded
          if (hasActiveChild) {
            if (item.label) {
              newExpandedItems[item.label] = true;
            }
          }
          
          // Continue checking deeper
          checkItems(item.items);
        }
      });
    };
    
    checkItems(sidebarConfig);
    
    // Update state with expanded items
    setExpandedItems(prev => ({
      ...prev,
      ...newExpandedItems
    }));
  }, [location.pathname]);

  // Toggle an accordion item
  const handleToggleAccordion = useCallback((label: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [label]: !prev[label]
    }));
  }, []);

  // Handle click outside to close popover
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        setShowPopover(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Toggle profile options popover
  const togglePopover = () => {
    setShowPopover(prev => !prev);
  };

  // Handle logout
  const handleLogout = () => {
    logout();
    setShowPopover(false);
  };

  // Add the hasAccess helper function
  const hasAccess = (roles?: Role[]) => {
    if (!roles || roles.length === 0) return true;
    const userRole = user?.role as Role;
    return roles.includes(userRole);
  };

  // Render a sidebar menu item
  const renderMenuItem = (item: SidebarConfigItem) => {
    if (!hasAccess(item.roles)) return null;
    
    const hasChildren = item.items && item.items.length > 0;
    const isActive = isRouteActive(item.to);
    const isExpanded = item.label ? expandedItems[item.label] === true : false;
    
    return (
      <div key={item.label} className={`sidebar-menu-item ${isActive ? 'active' : ''}`}>
        <div 
          className={`sidebar-menu-link ${hasChildren ? 'has-children' : ''} ${isActive ? 'active' : ''}`}
          onClick={() => {
            if (hasChildren && item.label) {
              handleToggleAccordion(item.label);
            } else if (item.to) {
              navigate({ to: item.to });
              // Close sidebar on mobile after navigation
              if (window.innerWidth <= 768) {
                closeSidebar();
              }
            }
          }}
        >
          <div className="sidebar-menu-content">
            {item.icon && <i className={item.icon}></i>}
            <span className={`sidebar-menu-text ${hasChildren ? 'parent-menu-text' : ''}`}>{item.label}</span>
            {item.isComingSoon && (
              <span className="coming-soon-chip">soon</span>
            )}
          </div>
          {hasChildren && (
            <i className={`sidebar-accordion-icon pi pi-chevron-right ${isExpanded ? 'chevron-expanded' : 'chevron-collapsed'}`}></i>
          )}
        </div>
        
        {/* Handle submenu with direct style display property */}
        {hasChildren && (
          <div className="sidebar-submenu" style={{ display: isExpanded ? 'block' : 'none' }}>
            {item.items?.map(child => renderMenuItem(child))}
          </div>
        )}
      </div>
    );
  };

  // Get initial character from username or email for avatar
  const getInitial = () => {
    if (!user) return '';
    return user.name ? user.name.charAt(0).toUpperCase() : 
           user.email ? user.email.charAt(0).toUpperCase() : '';
  };

  return (
    <>      
      {/* Mobile overlay - only show when sidebar is open */}
      {isOpen && (
        <div 
          className="sidebar-overlay open" 
          onClick={closeSidebar}
        ></div>
      )}
      
      {/* Sidebar - always render but use CSS transitions for show/hide */}
      <aside className={`sidebar-v2 ${isOpen ? 'open' : 'closed'}`}>
        <div className="sidebar-header">
          <h1 className="sidebar-title">{APP_NAME}</h1>
          <button className="sidebar-close-btn" onClick={closeSidebar}>
            <i className="pi pi-times"></i>
          </button>
        </div>
        <div className="sidebar-menu">
          {sidebarConfig.map(item => renderMenuItem(item))}
        </div>
        
        {/* Profile row at bottom */}
        <div className="sidebar-profile">
          <div className="sidebar-profile-avatar">
            {getInitial()}
          </div>
          <div className="sidebar-profile-info">
            <span className="sidebar-profile-name">
              {user?.name || 'User'}
            </span>
            <span className="sidebar-profile-role">
              {RoleLabels[user?.role || Role.ROLE_USER]}
            </span>
          </div>
          <div className="sidebar-profile-options" ref={popoverRef}>
            <button 
              className="sidebar-profile-more" 
              onClick={togglePopover}
            >
              <i className="pi pi-ellipsis-v"></i>
            </button>
            
            {/* Profile options popover */}
            {showPopover && (
              <div className="sidebar-profile-popover">
                <ul className="sidebar-profile-menu">
                  <li className="sidebar-profile-menu-item" onClick={handleLogout}>
                    <i className="pi pi-sign-out"></i>
                    <span>Logout</span>
                  </li>
                </ul>
              </div>
            )}
          </div>
        </div>
      </aside>
    </>
  );
};

export default SidebarV2;