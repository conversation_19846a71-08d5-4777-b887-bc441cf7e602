export const ROUTES = {
  // Public routes
  PUBLIC: {
    LOGIN: '/login',
  },
  // Private routes
  PRIVATE: {
    APP: '/app',
    DASHBOARD: '/dashboard',
    ORGANIZATION_DETAILS: '/organization/overview',
    USER_DETAILS: '/employee',
    LOCATION_DETAILS: '/location',
    DEPARTMENT_DETAILS: '/department',
    DESIGNATION_DETAILS: '/designation',
    ORGANIZATION_ITEM_CATALOG: '/organization/items',
    PRODUCT_CATALOG: '/items',
    ADD_USER: '/user/add',
    EDIT_USER: '/user/edit',
    ADD_PRODUCT: '/product/add',
    EDIT_PRODUCT: '/product/edit',
    ADD_ORG_PRODUCT: '/organization/product/add',
    EDIT_ORG_PRODUCT: '/organization/product/edit',
    SETTINGS: '/organization/settings',
    ADD_CATEGORY: '/category/add',
    EDIT_CATEGORY: '/category/edit',
    ADD_SUBCATEGORY: '/subcategory/add',
    EDIT_SUBCATEGORY: '/subcategory/edit',
    APPROVAL_WORKFLOWS: '/approval-workflows',
    ADD_APPROVAL_WORKFLOW: '/add',
    EDIT_APPROVAL_WORKFLOW: '/edit',
    ORGANIZATION_LIST: '/organization-list',
    ADD_ORGANIZATION: '/organization/add',
    EDIT_ORGANIZATION: '/organization/edit',
    PURCHASE_REQUESTS: '/purchase-requests',
    NEW_PURCHASE_REQUEST: '/purchase-requests/new',
  },
  // Common routes
  COMMON: {
    NOT_FOUND: '*',
  },
} as const;

export type RoutePath = typeof ROUTES.PUBLIC[keyof typeof ROUTES.PUBLIC] |
  typeof ROUTES.PRIVATE[keyof typeof ROUTES.PRIVATE] |
  typeof ROUTES.COMMON[keyof typeof ROUTES.COMMON];