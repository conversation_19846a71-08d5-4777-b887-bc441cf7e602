// OrganizationContext.tsx
import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';

// Define types
interface OrganizationContextType {
  organizationId: number | undefined;
  setOrganizationId: (id: number | undefined) => void;
}

interface OrganizationProviderProps {
  children: ReactNode;
}

// Create the context
const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

// Custom hook to use the organization context
export const useOrganizationContext = (): OrganizationContextType => {
  const context = useContext(OrganizationContext);
  if (!context) {
    throw new Error('useOrganization must be used within an OrganizationProvider');
  }
  return context;
};

// Provider component
export const OrganizationProvider: React.FC<OrganizationProviderProps> = ({ children }) => {
  // Initialize state by reading from localStorage synchronously
  const [organizationId, setOrganizationId] = useState<number | undefined>(() => {
    // This function runs only once during initialization
    try {
      const stored = localStorage.getItem('organizationId');
      return stored ? Number(stored) : undefined;
    } catch (error) {
      console.error('Failed to read organizationId from localStorage:', error);
      return undefined;
    }
  });

  // Save to localStorage whenever organizationId changes
  useEffect(() => {
    if (organizationId !== undefined) {
      localStorage.setItem('organizationId', organizationId.toString());
    } else {
      localStorage.removeItem('organizationId');
    }
  }, [organizationId]);

  const value = {
    organizationId,
    setOrganizationId,
  };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
};