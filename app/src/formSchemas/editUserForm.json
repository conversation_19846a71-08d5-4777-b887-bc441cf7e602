{"fields": [{"type": "text", "name": "name", "label": "Full Name", "placeholder": "Enter full name", "icon": "pi-user", "validation": {"required": false, "minLength": 2, "maxLength": 50}}, {"type": "text", "name": "email", "label": "Email Address", "placeholder": "Enter email address", "icon": "pi-envelope", "validation": {"required": false, "email": true, "maxLength": 100}}, {"type": "text", "name": "phoneNumber", "label": "Phone Number", "placeholder": "Enter phone number", "icon": "pi-phone", "validation": {"required": false, "maxLength": 20}}, {"type": "select", "name": "departmentId", "label": "Department", "placeholder": "Select department", "icon": "pi-sitemap", "validation": {"required": false}, "options": []}, {"type": "select", "name": "designationId", "label": "Designation", "placeholder": "Select designation", "icon": "pi-id-card", "validation": {"required": false}, "options": []}, {"type": "select", "name": "locationId", "label": "Location", "placeholder": "Select location", "icon": "pi-map-marker", "validation": {"required": false}, "options": []}, {"type": "text", "name": "employeeId", "label": "Employee ID", "placeholder": "Enter employee ID (optional)", "icon": "pi-tag", "validation": {"required": false, "maxLength": 50}}, {"type": "date", "name": "joiningDate", "label": "Joining Date", "placeholder": "Select joining date", "icon": "pi-calendar", "validation": {"required": false}}, {"type": "autocomplete", "name": "reportingManagerId", "label": "Reporting Manager", "placeholder": "Search for reporting manager", "icon": "pi-users", "validation": {"required": false}, "options": []}], "actions": [{"id": "submit", "type": "submit", "label": "Update User"}, {"id": "cancel", "type": "button", "label": "Cancel"}]}