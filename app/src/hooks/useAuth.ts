import { ROUTES } from '@/constants/routes.constant';
import { useOrganizationContext } from '@/context/OrganizationContext';
import { UserService } from '@/services/api/userService';
import { LoginRequest, Role } from '@/types/api/user';
import { useNavigate } from '@tanstack/react-router';
import { useCallback, useEffect, useState } from 'react';

interface User {
  id: number;
  name: string;
  email: string;
  role: Role;
  organizationId?: number;
}

interface AuthContext {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  userRole: Role | null;
}


export const useAuth = (): AuthContext => {
  const [user, setUser] = useState<User | null>(null);
  const [userRole, setUserRole] = useState<Role | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  const { setOrganizationId } = useOrganizationContext();

  useEffect(() => {
    if (user?.role) {
      setUserRole(user?.role);
    }
  }, [user])

  const clearAuthData = useCallback(() => {
    setUser(null);
    setIsAuthenticated(false);
    setOrganizationId(undefined)
    localStorage.removeItem('user');
    localStorage.removeItem('auth_token');
  }, []);

  useEffect(() => {
    const initializeAuth = () => {
      try {

        const storedUser = localStorage.getItem('user');
        const storedToken = localStorage.getItem('auth_token');

        if (storedUser && storedToken) {
          const parsedUser: User = JSON.parse(storedUser);
          if (parsedUser && parsedUser.id && parsedUser.name && parsedUser.email && parsedUser.role) {
            setUser(parsedUser);
            setIsAuthenticated(true);
            if(parsedUser.organizationId) {
              setOrganizationId(parsedUser.organizationId);
            }
          } else {
            clearAuthData();
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        clearAuthData();
      } finally {
        setIsLoading(false);
      }
    }
    initializeAuth();
  }, [clearAuthData]);

  const login = async (email: string, password: string) => {
    try {
      const loginRequest: LoginRequest = {
        email: email,
        password: password,
      }
      const response = await UserService.login(loginRequest);
      const data = JSON.parse(JSON.stringify(response));

      if (data && data.user && data.token) {
        setUser(data.user);
        setIsAuthenticated(true);
        localStorage.setItem('user', JSON.stringify(data.user));
        localStorage.setItem('auth_token', data.token);
        navigate({ to: ROUTES.PRIVATE.APP });
      } else {
        throw new Error('Invalid credentials');
      }
    } catch (error) {
      console.error('Login failed:', error);
      clearAuthData();
      throw error;
    }
  };

  const logout = () => {
    clearAuthData();
    navigate({ to: ROUTES.PUBLIC.LOGIN });
  };

  return {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    userRole,
  };
}; 