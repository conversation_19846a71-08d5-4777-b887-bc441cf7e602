/**
 * Custom hooks for catalog management operations
 */

import {
  createCategory,
  createOrganizationProduct,
  createProduct,
  createSubcategory,
  deleteCategory,
  deleteOrganizationProduct,
  deleteProduct,
  deleteSubcategory,
  getCategories,
  getCategoryById,
  getOrganizationProductById,
  getOrganizationProducts,
  getProductById,
  getProducts,
  getSubcategories,
  getSubcategoriesByCategoryId,
  getSubcategoryById,
  updateCategory,
  updateOrganizationProduct,
  updateProduct,
  updateSubcategory,
} from '@/services/api/catalogService';
import { QueryParams } from '@/types/api/common';
import {
  CreateCategoryRequest,
  CreateOrganizationProductRequest,
  CreateProductRequest,
  CreateSubcategoryRequest,
  UpdateCategoryRequest,
  UpdateOrganizationProductRequest,
  UpdateProductRequest,
  UpdateSubcategoryRequest
} from '@/types/catalog.types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

/**
 * Hook for fetching a paginated list of categories
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with categories data, loading state, and error state
 */
export const useCategories = (params?: QueryParams) => {
  return useQuery({
    queryKey: ['categories', params],
    queryFn: () => getCategories(params),
  });
};

/**
 * Hook for fetching a single category by ID
 * @param id Category ID
 * @returns Query result with category data, loading state, and error state
 */
export const useCategory = (id: number) => {
  return useQuery({
    queryKey: ['category', id],
    queryFn: () => getCategoryById(id),
    enabled: !!id, // Only run the query if id is provided
  });
};

/**
 * Hook for creating a new category
 * @returns Mutation for creating a category with loading, error, and success states
 */
export const useCreateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCategoryRequest) => createCategory(data),
    onSuccess: () => {
      // Invalidate the categories query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
  });
};

/**
 * Hook for updating an existing category
 * @returns Mutation for updating a category with loading, error, and success states
 */
export const useUpdateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateCategoryRequest }) =>
      updateCategory(id, data),
    onSuccess: (_, variables) => {
      // Invalidate the specific category query and the categories list
      queryClient.invalidateQueries({ queryKey: ['category', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
  });
};

/**
 * Hook for deleting a category
 * @returns Mutation for deleting a category with loading, error, and success states
 */
export const useDeleteCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => deleteCategory(id),
    onSuccess: () => {
      // Invalidate the categories query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['categories'] });
    },
  });
};

/**
 * Hook for fetching a paginated list of subcategories
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with subcategories data, loading state, and error state
 */
export const useSubcategories = (params?: QueryParams) => {
  // Extract category_id from params for better query key structure
  const categoryId = params?.filter?.category_id;

  return useQuery({
    queryKey: ['subcategories', 'all', { categoryId, ...params }],
    queryFn: () => getSubcategories(params),
  });
};

/**
 * Hook for fetching subcategories by category ID
 * @param categoryId Category ID
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with subcategories data, loading state, and error state
 */
export const useSubcategoriesByCategory = (categoryId: number | null, params?: QueryParams) => {
  return useQuery({
    queryKey: ['subcategories', categoryId, params],
    queryFn: () => getSubcategoriesByCategoryId(categoryId!, params),
    enabled: !!categoryId, // Only run the query if categoryId is provided
  });
};

/**
 * Hook for fetching a single subcategory by ID
 * @param categoryId Category ID
 * @param id Subcategory ID
 * @returns Query result with subcategory data, loading state, and error state
 */
export const useSubcategory = (categoryId: number, id: number) => {
  return useQuery({
    queryKey: ['subcategory', categoryId, id],
    queryFn: () => getSubcategoryById(categoryId, id),
    enabled: !!(categoryId && id), // Only run the query if both IDs are provided
  });
};

/**
 * Hook for creating a new subcategory
 * @returns Mutation for creating a subcategory with loading, error, and success states
 */
export const useCreateSubcategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSubcategoryRequest) => createSubcategory(data),
    onSuccess: (_, variables) => {
      // Invalidate the subcategories query and the subcategories by category query
      queryClient.invalidateQueries({ queryKey: ['subcategories'] });
      if (variables.categoryId) {
        queryClient.invalidateQueries({ queryKey: ['subcategories', variables.categoryId] });
      }
    },
  });
};

/**
 * Hook for updating an existing subcategory
 * @returns Mutation for updating a subcategory with loading, error, and success states
 */
export const useUpdateSubcategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ categoryId, id, data }: { categoryId: number; id: number; data: UpdateSubcategoryRequest }) =>
      updateSubcategory(categoryId, id, data),
    onSuccess: (_, variables) => {
      // Invalidate the specific subcategory query and the subcategories list
      queryClient.invalidateQueries({ queryKey: ['subcategory', variables.categoryId, variables.id] });
      queryClient.invalidateQueries({ queryKey: ['subcategories'] });
      queryClient.invalidateQueries({ queryKey: ['subcategories', variables.categoryId] });
    },
  });
};

/**
 * Hook for deleting a subcategory
 * @returns Mutation for deleting a subcategory with loading, error, and success states
 */
export const useDeleteSubcategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ categoryId, id }: { categoryId: number; id: number }) =>
      deleteSubcategory(categoryId, id),
    onSuccess: () => {
      // Invalidate the subcategories query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['subcategories'] });
    },
  });
};

/**
 * Hook for fetching a paginated list of products
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with products data, loading state, and error state
 */
export const useProducts = (params?: QueryParams) => {
  return useQuery({
    queryKey: ['products', params],
    queryFn: () => getProducts(params),
  });
};

/**
 * Hook for fetching a single product by ID
 * @param id Product ID
 * @returns Query result with product data, loading state, and error state
 */
export const useProduct = (id: number) => {
  return useQuery({
    queryKey: ['product', id],
    queryFn: () => getProductById(id),
    enabled: !!id, // Only run the query if id is provided
  });
};

/**
 * Hook for creating a new product
 * @returns Mutation for creating a product with loading, error, and success states
 */
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProductRequest) => createProduct(data),
    onSuccess: () => {
      // Invalidate the products query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

/**
 * Hook for updating an existing product
 * @returns Mutation for updating a product with loading, error, and success states
 */
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateProductRequest }) =>
      updateProduct(id, data),
    onSuccess: (_, variables) => {
      // Invalidate the specific product query and the products list
      queryClient.invalidateQueries({ queryKey: ['product', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

/**
 * Hook for deleting a product
 * @returns Mutation for deleting a product with loading, error, and success states
 */
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => deleteProduct(id),
    onSuccess: () => {
      // Invalidate the products query to refetch the data
      queryClient.invalidateQueries({ queryKey: ['products'] });
    },
  });
};

/**
 * Hook for fetching a paginated list of organization products
 * @param organizationId Organization ID
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with organization products data, loading state, and error state
 */
export const useOrganizationProducts = (organizationId: number, params?: QueryParams) => {
  return useQuery({
    queryKey: ['organizationProducts', organizationId, params],
    queryFn: () => getOrganizationProducts(organizationId, params),
    enabled: !!organizationId, // Only run the query if organizationId is provided
  });
};

/**
 * Hook for fetching a single organization product by ID
 * @param organizationId Organization ID
 * @param id Organization Product ID
 * @returns Query result with organization product data, loading state, and error state
 */
export const useOrganizationProduct = (organizationId: number, id: number) => {
  return useQuery({
    queryKey: ['organizationProduct', organizationId, id],
    queryFn: () => getOrganizationProductById(organizationId, id),
    enabled: !!(organizationId && id), // Only run the query if both IDs are provided
  });
};

/**
 * Hook for creating a new organization product
 * @returns Mutation for creating an organization product with loading, error, and success states
 */
export const useCreateOrganizationProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOrganizationProductRequest) => createOrganizationProduct(data),
    onSuccess: (_, variables) => {
      // Invalidate the organization products query to refetch the data
      if (variables.organizationId) {
        queryClient.invalidateQueries({ queryKey: ['organizationProducts', variables.organizationId] });
      }
    },
  });
};

/**
 * Hook for updating an existing organization product
 * @returns Mutation for updating an organization product with loading, error, and success states
 */
export const useUpdateOrganizationProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateOrganizationProductRequest }) =>
      updateOrganizationProduct(id, data),
    onSuccess: (_, variables) => {
      // Invalidate the specific organization product query and the organization products list
      if (variables.data.organizationId) {
        queryClient.invalidateQueries({ queryKey: ['organizationProduct', variables.data.organizationId, variables.id] });
        queryClient.invalidateQueries({ queryKey: ['organizationProducts', variables.data.organizationId] });
      }
    },
  });
};

/**
 * Hook for deleting an organization product
 * @returns Mutation for deleting an organization product with loading, error, and success states
 */
export const useDeleteOrganizationProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ organizationId, id }: { organizationId: number; id: number }) =>
      deleteOrganizationProduct(organizationId, id),
    onSuccess: (_, variables) => {
      // Invalidate the organization products query to refetch the data
      if (variables.organizationId) {
        queryClient.invalidateQueries({ queryKey: ['organizationProducts', variables.organizationId] });
      }
    },
  });
};
