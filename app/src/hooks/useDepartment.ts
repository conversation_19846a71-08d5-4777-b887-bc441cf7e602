/**
 * Custom hooks for department management operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { DepartmentService } from '@/services/api/departmentService';
import {
  DepartmentDTO,
  CreateDepartmentRequest,
  UpdateDepartmentRequest,
  LegacyCreateDepartmentRequest,
  LegacyUpdateDepartmentRequest
} from '@/types/department.types';
import { QueryParams } from '@/types/api/common';

/**
 * Hook for fetching departments from API
 * @param organizationId Optional organization ID to filter departments
 * @returns Query result with departments data, loading state, and error state
 */
export const useDepartments = (organizationId: number) => {
  return useQuery({
    queryKey: ['departments', organizationId],
    queryFn: () => DepartmentService.getDepartments(organizationId),
  });
};

/**
 * Hook for fetching a paginated list of departments (legacy method for backward compatibility)
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with departments data, loading state, and error state
 */
export const useDepartmentsPaginated = (params?: QueryParams) => {
  return useQuery({
    queryKey: ['departments-paginated', params],
    queryFn: () => DepartmentService.getDepartmentsPaginated(params),
  });
};

/**
 * Hook for fetching a single department by ID
 * @param id Department ID
 * @returns Query result with department data, loading state, and error state
 */
export const useDepartment = (id: number, organizationId: number) => {
  return useQuery({
    queryKey: ['department', id, organizationId],
    queryFn: () => DepartmentService.getDepartmentById(id, organizationId),
    enabled: !!id && !!organizationId,
  });
};

/**
 * Hook for fetching a single department by ID (legacy method for backward compatibility)
 * @param id Department ID as string
 * @returns Query result with department data, loading state, and error state
 */
export const useDepartmentLegacy = (id: string) => {
  return useQuery({
    queryKey: ['department-legacy', id],
    queryFn: () => DepartmentService.getDepartmentByIdLegacy(id),
    enabled: !!id, // Only run the query if an ID is provided
  });
};

/**
 * Hook for creating a new department
 * @param organizationId Organization ID for the department
 * @returns Mutation result with create function, loading state, and error state
 */
export const useCreateDepartment = (organizationId: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => DepartmentService.createDepartment(data, organizationId),
    onSuccess: () => {
      // Invalidate departments query to refetch the list
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      queryClient.invalidateQueries({ queryKey: ['departments-paginated'] });
    },
  });
};

/**
 * Hook for creating a new department (legacy method for backward compatibility)
 * @returns Mutation result with create function, loading state, and error state
 */
export const useCreateDepartmentLegacy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: LegacyCreateDepartmentRequest) => DepartmentService.createDepartmentLegacy(data),
    onSuccess: () => {
      // Invalidate departments query to refetch the list
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      queryClient.invalidateQueries({ queryKey: ['departments-paginated'] });
    },
  });
};

/**
 * Hook for updating an existing department
 * @param id Department ID
 * @param organizationId Organization ID for the department
 * @returns Mutation result with update function, loading state, and error state
 */
export const useUpdateDepartment = (id: number, organizationId: number) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: any) => DepartmentService.updateDepartment(id, data, organizationId),
    onSuccess: () => {
      // Invalidate specific department and departments list queries
      queryClient.invalidateQueries({ queryKey: ['department', id] });
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      queryClient.invalidateQueries({ queryKey: ['departments-paginated'] });
    },
  });
};

/**
 * Hook for updating an existing department (legacy method for backward compatibility)
 * @param id Department ID as string
 * @returns Mutation result with update function, loading state, and error state
 */
export const useUpdateDepartmentLegacy = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: LegacyUpdateDepartmentRequest) => DepartmentService.updateDepartmentLegacy(id, data),
    onSuccess: () => {
      // Invalidate specific department and departments list queries
      queryClient.invalidateQueries({ queryKey: ['department-legacy', id] });
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      queryClient.invalidateQueries({ queryKey: ['departments-paginated'] });
    },
  });
};

/**
 * Hook for deleting a department
 * @returns Mutation result with delete function, loading state, and error state
 */
export const useDeleteDepartment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, organizationId }: { id: number; organizationId: number }) =>
      DepartmentService.deleteDepartment(id, organizationId),
    onSuccess: () => {
      // Invalidate departments query to refetch the list
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      queryClient.invalidateQueries({ queryKey: ['departments-paginated'] });
    },
  });
};

/**
 * Hook for deleting a department (legacy method for backward compatibility)
 * @returns Mutation result with delete function, loading state, and error state
 */
export const useDeleteDepartmentLegacy = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, organizationId }: { id: number; organizationId: number }) =>
      DepartmentService.deleteDepartment(id, organizationId),
    onSuccess: () => {
      // Invalidate departments query to refetch the list
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      queryClient.invalidateQueries({ queryKey: ['departments-paginated'] });
    },
  });
};


