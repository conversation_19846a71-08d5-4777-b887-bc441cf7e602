import { LocationService } from '@/services/api/locationService';
import { CreateLocationRequest, UpdateLocationRequest } from '@/types/location.types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

/**
 * Hook for fetching a paginated list of locations
 */
export const useLocations = (organizationId: number) => {
  return useQuery({
    queryKey: ['locations', organizationId],
    queryFn: () => LocationService.getAllLocations(organizationId),
  });
};

/**
 * Hook for fetching a single location by ID
 */
export const useLocation = (id: number, organizationId: number) => {
  return useQuery({
    queryKey: ['location', id, organizationId],
    queryFn: () => LocationService.getLocationById(id, organizationId),
    enabled: !!id && !!organizationId,
  });
};

/**
 * Hook for creating a new location
 */
export const useCreateLocation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { formData: CreateLocationRequest; organizationId: number }) =>
      LocationService.createLocation(data.formData, data.organizationId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['locations', variables.organizationId] });
    },
  });
};

/**
 * Hook for updating an existing location
 */
export const useUpdateLocation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data, organizationId }: { id: number; data: UpdateLocationRequest; organizationId: number }) =>
      LocationService.updateLocation(id, data, organizationId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['location', variables.id] });
      queryClient.invalidateQueries({ queryKey: ['locations', variables.organizationId] });
    },
  });
};

/**
 * Hook for deleting a location
 */
export const useDeleteLocation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, organizationId }: { id: number; organizationId: number }) =>
      LocationService.deleteLocation(id, organizationId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['locations', variables.organizationId] });
    },
  });
};

/**
 * Hook for setting a location as primary
 */
export const useSetPrimaryLocation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, organizationId }: { id: number; organizationId: number }) =>
      LocationService.setPrimaryLocation(id, organizationId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['locations', variables.organizationId] });
      queryClient.invalidateQueries({ queryKey: ['location', variables.id] });
    },
  });
};