/**
 * Custom hooks for organization management operations
 */

import { useOrganizationContext } from '@/context/OrganizationContext';
import { LocationService } from '@/services/api/locationService';
import { OrganizationService } from '@/services/api/organizationService';
import {
  CreateOrganizationRequest,
  UpdateOrganizationRequest
} from '@/types/organization.types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

/**
 * Hook for fetching a single organization by ID
 * @param id Organization ID
 * @returns Query result with organization data, loading state, and error state
 */
export const useOrganization = (id: number) => {
  return useQuery({
    queryKey: ['organization', id],
    queryFn: () => OrganizationService.getOrganizationById(id),
    enabled: !!id, // Only run the query if an ID is provided
  });
};

/**
 * Hook for fetching all organizations with detailed information
 * @returns Query result with organizations data, loading state, and error state
 */
export const useAllOrganizationsDetail = () => {
  return useQuery({
    queryKey: ['organizations', 'detail'],
    queryFn: () => OrganizationService.getAllOrganizationsDetail(),
  });
};

/**
 * Hook for fetching all organizations with short information
 * @returns Query result with organizations data, loading state, and error state
 */
export const useAllOrganizationsShort = () => {
  return useQuery({
    queryKey: ['organizations', 'short'],
    queryFn: () => OrganizationService.getAllOrganizationsShort(),
  });
};

/**
 * Hook for creating a new organization
 * @returns Mutation object for creating organization
 */
export const useCreateOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateOrganizationRequest) => {
      const requestData = {
        ...data,
        enabled: data.enabled ?? true
      };
      return OrganizationService.createOrganization(requestData);
    },
    onSuccess: () => {
      // Invalidate and refetch organization queries
      queryClient.invalidateQueries({ queryKey: ['organizations'] });
    },
  });
};

/**
 * Hook for updating an organization
 * @returns Mutation object for updating organization
 */
export const useUpdateOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateOrganizationRequest }) =>
      OrganizationService.updateOrganization(id, data),
    onSuccess: (_, variables) => {
      // Invalidate and refetch organization queries
      queryClient.invalidateQueries({ queryKey: ['organizations'] });
      queryClient.invalidateQueries({ queryKey: ['organization', variables.id] });
    },
  });
};

/**
 * Hook for deleting an organization
 * @returns Mutation object for deleting organization
 */
export const useDeleteOrganization = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => OrganizationService.deleteOrganization(id),
    onSuccess: () => {
      // Invalidate and refetch organization queries
      queryClient.invalidateQueries({ queryKey: ['organizations'] });
    },
  });
};

/**
 * Hook for fetching primary location for an organization
 * @param organizationId Organization ID
 * @returns Query result with primary location data, loading state, and error state
 */
export const usePrimaryLocation = (organizationId: number) => {
  return useQuery({
    queryKey: ['primaryLocation', organizationId],
    queryFn: () => LocationService.getPrimaryLocation(organizationId),
    enabled: !!organizationId, // Only run the query if an organization ID is provided
  });
};

/**
 * Hook for getting the current user's organization
 * TODO: This should be replaced with proper user context when authentication is implemented
 * For now, we'll use the provided organization ID
 */
export const useCurrentOrganization = () => {
  const { organizationId } = useOrganizationContext();

  return useOrganization(organizationId as number);
};

/**
 * Hook for getting the current user's organization primary location
 */
export const useCurrentOrganizationPrimaryLocation = () => {
  const { organizationId } = useOrganizationContext();

  return usePrimaryLocation(organizationId as number);
};
