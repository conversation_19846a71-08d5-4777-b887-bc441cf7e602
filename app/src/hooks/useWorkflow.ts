/**
 * Custom hooks for workflow record approval operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { WorkflowService } from '@/services/api/workflowService';
import { ApprovalActionRequest } from '@/types/approvalWorkflow.types';

/**
 * Hook for fetching pending approvals for a user
 * @param organizationId Organization ID
 * @param userId User ID
 * @returns Query result with pending approvals data, loading state, and error state
 */
export const usePendingApprovals = (organizationId: number, userId: number) => {
  return useQuery({
    queryKey: ['pendingApprovals', organizationId, userId],
    queryFn: () => WorkflowService.getPendingApprovals(organizationId, userId),
    enabled: !!organizationId && !!userId,
  });
};

/**
 * Hook for fetching initiated approvals for a user
 * @param organizationId Organization ID
 * @param userId User ID
 * @returns Query result with initiated approvals data, loading state, and error state
 */
export const useInitiatedApprovals = (organizationId: number, userId: number) => {
  return useQuery({
    queryKey: ['initiatedApprovals', organizationId, userId],
    queryFn: () => WorkflowService.getInitiatedApprovals(organizationId, userId),
    enabled: !!organizationId && !!userId,
  });
};

/**
 * Hook for approving a record
 * @returns Mutation function and state for approving a record
 */
export const useApproveRecord = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      organizationId: number;
      recordApprovalId: number;
      request: ApprovalActionRequest;
    }) =>
      WorkflowService.approveRecord(
        data.organizationId,
        data.recordApprovalId,
        data.request
      ),
    onSuccess: (_, variables) => {
      // Invalidate pending approvals to refetch the data
      queryClient.invalidateQueries({
        queryKey: ['pendingApprovals', variables.organizationId, variables.request.userId]
      });
      // Also invalidate purchase requests to reflect status changes
      queryClient.invalidateQueries({
        queryKey: ['purchaseRequests', variables.organizationId]
      });
    },
  });
};

/**
 * Hook for rejecting a record
 * @returns Mutation function and state for rejecting a record
 */
export const useRejectRecord = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      organizationId: number;
      recordApprovalId: number;
      request: ApprovalActionRequest;
    }) =>
      WorkflowService.rejectRecord(
        data.organizationId,
        data.recordApprovalId,
        data.request
      ),
    onSuccess: (_, variables) => {
      // Invalidate pending approvals to refetch the data
      queryClient.invalidateQueries({
        queryKey: ['pendingApprovals', variables.organizationId, variables.request.userId]
      });
      // Also invalidate purchase requests to reflect status changes
      queryClient.invalidateQueries({
        queryKey: ['purchaseRequests', variables.organizationId]
      });
    },
  });
};
