import React from 'react'
import ReactDOM from 'react-dom/client'
import { QueryProvider } from '@/providers/QueryProvider'
import { PrimeReactProvider } from 'primereact/api'
import { SidebarProvider } from '@/context/SidebarContext'
import { App } from './App'
import '@/styles/index.css'
import '@/styles/prime-theme.css'
import { OrganizationProvider } from './context/OrganizationContext'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <PrimeReactProvider>
      <OrganizationProvider>
        <QueryProvider>
          <SidebarProvider>
            <App />
          </SidebarProvider>
        </QueryProvider>
      </OrganizationProvider>
    </PrimeReactProvider>
  </React.StrictMode>,
)