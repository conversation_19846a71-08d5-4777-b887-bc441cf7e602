import type { FormSchema } from '@/components/form/DynamicForm';
import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import loginFormSchemaJson from '@/formSchemas/loginForm.json';
import { useAuth } from '@/hooks/useAuth';
import { dashboardRoute } from '@/routes/private/dashboard.route';
import '@/styles/login.css';
import { Navigate, useLocation, useNavigate } from '@tanstack/react-router';
import { ProgressSpinner } from 'primereact/progressspinner';
import { useState } from 'react';

const loginFormSchema = loginFormSchemaJson as FormSchema;

export const Login = () => {
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const { isAuthenticated, login, isLoading: isAuthLoading } = useAuth();
  const from = location.state?.from?.pathname || dashboardRoute.to;

  if(isAuthLoading) {
    return <ProgressSpinner />;
  }

  if (isAuthenticated) {
    return <Navigate to={from} />;
  }

  const handleSubmit = async (data: any) => {
    try {
      setError('');
      setLoading(true);
      await login(data.email, data.password);
      navigate({ to: from, replace: true });
    } catch (err) {
      setError('Invalid email or password');
      console.error('Login error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex align-items-center justify-content-center min-h-screen" style={{ position: 'relative' }}>
      <img
        src="/assets/wave-green-fullscreen.svg"
        alt="Wave Background"
        className="fixed left-0 top-0 min-h-screen min-w-screen"
        style={{ zIndex: -1 }}
      />
      <Card
        title="Log in"
        subtitle="Please enter your details"
        variant="elevated"
        padding="large"
        className="login-card"
      >
        <DynamicForm schema={loginFormSchema} onSubmit={handleSubmit} defaultValues={{ email: "", password: "" }} />
        {error && <div className="p-error mb-3" style={{ textAlign: 'center' }}>{error}</div>}
      </Card>
    </div>
  );
}; 