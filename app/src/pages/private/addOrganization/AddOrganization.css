@import url('../../../variables.css');

.add-organization .card {
    background: var(--surface-card);
    border-radius: var(--border-radius);
    padding: var(--card-padding-screen);
    box-shadow: var(--card-shadow);
}

.form-section {
    margin-bottom: 1.5rem;
}

.form-fields-container {
    flex-direction: column;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
    .add-organization .card {
        padding: var(--card-padding-mobile);
    }
}
