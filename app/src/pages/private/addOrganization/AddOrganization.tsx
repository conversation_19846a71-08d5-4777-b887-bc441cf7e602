import { DynamicForm } from '@/components/form/DynamicForm';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import { useNavigate } from '@tanstack/react-router';
import React, { useRef, useState } from 'react';

import type { FormSchema } from '@/components/form/DynamicForm';
import Typography from '@/components/ui/Typography';
import { ROUTES } from '@/constants/routes.constant';
import { useCreateOrganization } from '@/hooks/useOrganization';
import organizationFormSchema from '@/formSchemas/organizationOnboarding.json';
import './AddOrganization.css';

const AddOrganization: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);
  const [loading, setLoading] = useState<boolean | undefined>();

  // Create organization mutation
  const createOrganizationMutation = useCreateOrganization();

  // Handle form submission
  const handleSubmit = async (data: any) => {
    setLoading(true)
    try {
      const organizationData = {
        name: data.name,
        description: data.description,
        organizationAdmin: {
          name: data.adminName,
          email: data.email,
          phoneNumber: data.phoneNumber,
          gstin: data.gstin
        }
      };

      await createOrganizationMutation.mutateAsync(organizationData);

      // Show success toast and navigate back
      toast.current?.showSuccess('Organization created successfully');
      navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.ORGANIZATION_LIST });
    } catch (error) {
      toast.current?.showError('Failed to create organization');
    } finally {
      setLoading(false)
    }
  };

  // Handle cancel button
  const handleCancel = () => {
    navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.ORGANIZATION_LIST });
  };

  return (
    <div className="add-organization p-4">
      <Toast ref={toast} position="top-right" />
      <Typography variant='h5' weight='semibold' className="mb-4">Add Organization</Typography>
      <DynamicForm
        isSubmitButtonLoading={loading}
        schema={organizationFormSchema as FormSchema}
        onSubmit={handleSubmit}
        className="mt-4"
        buttonHandlers={{
          cancel: handleCancel
        }}
      />
    </div>
  );
};

export default AddOrganization;
