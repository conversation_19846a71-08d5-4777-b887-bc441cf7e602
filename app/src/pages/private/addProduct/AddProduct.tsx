import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import { useNavigate } from '@tanstack/react-router';
import React, { useRef, useState } from 'react';

import type { FormSchema } from '@/components/form/DynamicForm';
import { useOrganizationContext } from '@/context/OrganizationContext';
import productFormSchemaJson from '@/formSchemas/productForm.json';
import { useCategories, useCreateOrganizationProduct, useSubcategoriesByCategory } from '@/hooks/useCatalog';
import { organizationItemCatalogRoute } from '@/routes/private/organizationItemCatalog.route';
import './AddProduct.css';

const AddProduct: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);
  const { organizationId } = useOrganizationContext();
  const safeOrgId = typeof organizationId === 'number' && !isNaN(organizationId) ? organizationId : 0;

  // Fetch categories (organization-aware)
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];

  // State for selected category
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);

  // Fetch subcategories based on selected category (organization-aware)
  const validCategoryId = typeof selectedCategoryId === 'number' && !isNaN(selectedCategoryId) ? selectedCategoryId : null;
  const { data: subcategoriesData } = useSubcategoriesByCategory(validCategoryId);
  const subcategories = subcategoriesData?.data || [];

  // Create organization product mutation
  const createOrganizationProductMutation = useCreateOrganizationProduct();

  // Compute the form schema with updated category/subcategory options
  const productFormSchema: FormSchema = React.useMemo(() => {
    const schema: FormSchema = JSON.parse(JSON.stringify(productFormSchemaJson));
    const categoryField = schema.fields.find(field => field.name === 'categoryId');
    if (categoryField) {
      categoryField.options = categories.map(category => ({
        label: category.name,
        value: String(category.id),
      }));
    }
    const subcategoryField = schema.fields.find(field => field.name === 'subCategoryId');
    if (subcategoryField) {
      subcategoryField.options = subcategories.map(subcategory => ({
        label: subcategory.name,
        value: String(subcategory.id),
      }));
    }
    return schema;
  }, [categories, subcategories]);

  // Handle category change
  const handleCategoryChange = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
  };

  // Handle form field changes
  const handleFormFieldChange = (field: string, value: any) => {
    if (field === 'categoryId') {
      handleCategoryChange(typeof value === 'string' ? parseInt(value) : value);
    }
  };

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      // Convert attributes array to object, omitting empty keys/values
      let attributesJson = undefined;
      if (Array.isArray(data.attributes)) {
        const attrObj: Record<string, string> = {};
        data.attributes.forEach((item: any) => {
          if (item.key && item.value) attrObj[item.key] = item.value;
        });
        attributesJson = Object.keys(attrObj).length > 0 ? JSON.stringify(attrObj) : undefined;
      }

      const orgProductData = {
        name: data.name,
        description: data.description || "",
        categoryId: typeof data.categoryId === 'string' ? parseInt(data.categoryId) : (typeof data.categoryId === 'number' ? data.categoryId : 0),
        subCategoryId: typeof data.subCategoryId === 'string' ? parseInt(data.subCategoryId) : (typeof data.subCategoryId === 'number' ? data.subCategoryId : 0),
        organizationId: safeOrgId,
        imageFile: data.imageFile || null,
        attributes: attributesJson,
      };

      // Create organization product
      await createOrganizationProductMutation.mutateAsync(orgProductData);

      // Show success toast and navigate back
      toast.current?.showSuccess('Product created successfully');
      navigate({ to: organizationItemCatalogRoute.to });
    } catch (error) {
      console.error('Error creating organization product:', error);
      toast.current?.showError('Failed to create product');
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate({ to: organizationItemCatalogRoute.to });
  };

  // Get default values for the form
  const getDefaultValues = () => {
    return {
      categoryId: null,
      subCategoryId: null
    };
  };

  return (
    <div className="add-product p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Add Product"
        subtitle="Enter product details"
        variant="elevated"
        padding="large"
        className="max-w-3xl mx-auto"
      >
        <DynamicForm
          schema={productFormSchema}
          onSubmit={handleSubmit}
          defaultValues={getDefaultValues()}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
          onFieldChange={handleFormFieldChange}
        />
      </Card>
    </div>
  );
};

export default AddProduct;
