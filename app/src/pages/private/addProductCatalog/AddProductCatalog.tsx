import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import { useNavigate } from '@tanstack/react-router';
import React, { useRef, useState } from 'react';

import type { FormSchema } from '@/components/form/DynamicForm';
import productFormSchemaJson from '@/formSchemas/productForm.json';
import { useCategories, useCreateProduct, useSubcategoriesByCategory } from '@/hooks/useCatalog';
import { productCatalogRoute } from '@/routes/private/productCatalog.route';
import { CreateProductRequest } from '@/types/catalog.types';
import './AddProductCatalog.css';

const AddProductCatalog: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);

  // Fetch categories
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];

  // State for selected category
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);

  // Fetch subcategories based on selected category
  const validCategoryId = typeof selectedCategoryId === 'number' && !isNaN(selectedCategoryId) ? selectedCategoryId : null;
  const { data: subcategoriesData } = useSubcategoriesByCategory(validCategoryId);
  const subcategories = subcategoriesData?.data || [];

  // Create product mutation
  const createProductMutation = useCreateProduct();

  // Compute the form schema with updated category/subcategory options
  const productFormSchema: FormSchema = React.useMemo(() => {
    const schema: FormSchema = JSON.parse(JSON.stringify(productFormSchemaJson));
    const categoryField = schema.fields.find(field => field.name === 'categoryId');
    if (categoryField) {
      categoryField.options = categories.map(category => ({
        label: category.name,
        value: String(category.id),
      }));
    }
    const subcategoryField = schema.fields.find(field => field.name === 'subCategoryId');
    if (subcategoryField) {
      subcategoryField.options = subcategories.map(subcategory => ({
        label: subcategory.name,
        value: String(subcategory.id),
      }));
    }
    return schema;
  }, [categories, subcategories]);

  // Handle category change
  const handleCategoryChange = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
  };

  // Handle form field changes
  const handleFormFieldChange = (field: string, value: any) => {
    if (field === 'categoryId') {
      handleCategoryChange(typeof value === 'string' ? parseInt(value) : value);
    }
  };

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      // Convert attributes array to object, omitting empty keys/values
      let attributesJson = undefined;
      if (Array.isArray(data.attributes)) {
        const attrObj: Record<string, string> = {};
        data.attributes.forEach((item: any) => {
          if (item.key && item.value) attrObj[item.key] = item.value;
        });
        attributesJson = Object.keys(attrObj).length > 0 ? JSON.stringify(attrObj) : undefined;
      }

      const productData: CreateProductRequest = {
        name: data.name,
        description: data.description,
        categoryId: typeof data.categoryId === 'string' ? parseInt(data.categoryId) : (typeof data.categoryId === 'number' ? data.categoryId : 0),
        subCategoryId: typeof data.subCategoryId === 'string' ? parseInt(data.subCategoryId) : (typeof data.subCategoryId === 'number' ? data.subCategoryId : 0),
        imageFile: data.imageFile || null,
        attributes: attributesJson,
      };
      console.log(productData)

      // Create product
      await createProductMutation.mutateAsync(productData);

      // Show success toast and navigate back
      toast.current?.showSuccess('Product created successfully');
      navigate({ to: productCatalogRoute.to });
    } catch (error) {
      console.error('Error creating product:', error);
      toast.current?.showError('Failed to create product');
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate({ to: productCatalogRoute.to });
  };

  // Get default values for the form
  const getDefaultValues = () => {
    return {
      categoryId: null,
      subCategoryId: null
    };
  };

  return (
    <div className="add-product p-4">
      <Toast ref={toast} position="top-right" />
      <Card
        title="Add Product"
        subtitle="Enter product details"
        variant="elevated"
        padding="large"
        className="max-w-3xl mx-auto"
      >
        <DynamicForm
          schema={productFormSchema}
          onSubmit={handleSubmit}
          defaultValues={getDefaultValues()}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
          onFieldChange={handleFormFieldChange}
        />
      </Card>
    </div>
  );
};

export default AddProductCatalog;
