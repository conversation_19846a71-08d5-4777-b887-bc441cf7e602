import { DynamicForm } from '@/components/form/DynamicForm';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import { useNavigate } from '@tanstack/react-router';
import React, { useEffect, useRef, useState } from 'react';

import type { FormSchema } from '@/components/form/DynamicForm';
import Typography from '@/components/ui/Typography';
import { ROUTES } from '@/constants/routes.constant';
import { useOrganizationContext } from '@/context/OrganizationContext';
import userFormSchemaJson from '@/formSchemas/userForm.json';
import { useDepartments } from '@/hooks/useDepartment';
import { useDesignations } from '@/hooks/useDesignation';
import { useCreateUser } from '@/hooks/useUser';
import { UserCreateRequest } from '@/types/api/user';
import './AddUser.css';
import { useLocations } from '@/hooks/useLocation';

const AddUser: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);

  const { organizationId } = useOrganizationContext();

  // State for form schema
  const [userFormSchema, setUserFormSchema] = useState<FormSchema>(userFormSchemaJson as FormSchema);

  // Fetch departments and designations for dropdowns
  const { data: departmentsData } = useDepartments(organizationId);
  const { data: designationsData } = useDesignations(organizationId);
  const { data: locationData } = useLocations(organizationId);
  const departments = departmentsData || [];
  const designations = designationsData?.data || [];
  const locations = locationData || [];

  // Create user mutation
  const createUserMutation = useCreateUser();

  // Update form schema with department and designation options
  useEffect(() => {
    const updatedSchema = { ...userFormSchema };

    // Update department options
    const departmentField = updatedSchema.fields.find(field => field.name === 'departmentId');
    if (departmentField && departmentField.type === 'select') {
      departmentField.options = departments.map(dept => ({
        label: dept.name,
        value: dept.id?.toString() || ''
      }));
    }

    // Update designation options
    const designationField = updatedSchema.fields.find(field => field.name === 'designationId');
    if (designationField && designationField.type === 'select') {
      designationField.options = designations.map(designation => ({
        label: designation.name,
        value: designation.id.toString()
      }));
    }

    // Update location options
    const locationField = updatedSchema.fields.find(field => field.name === 'locationId');
    if (locationField && locationField.type === 'select') {
      locationField.options = locations.map(location => ({
        label: location.name,
        value: location.id.toString()
      }));
    }

    setUserFormSchema(updatedSchema);
  }, [departments, designations, locations]);

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      const userData: UserCreateRequest = {
        email: data.email,
        password: data.password,
        name: data.name,
        phoneNumber: data.phoneNumber,
        organizationId: organizationId,
        departmentId: parseInt(data.departmentId),
        designationId: parseInt(data.designationId),
        locationId: parseInt(data.locationId),
        employeeId: data.employeeId || undefined,
        joiningDate: data.joiningDate ? data.joiningDate.toISOString().split('T')[0] : undefined,
        reportingManagerId: data.reportingManagerId || undefined,
      };

      await createUserMutation.mutateAsync({ data: userData, organizationId });

      // Show success toast and navigate back
      toast.current?.showSuccess('User created successfully');
      navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.USER_DETAILS });
    } catch (error) {
      toast.current?.showError('Failed to create user');
    }
  };

  // Handle cancel button
  const handleCancel = () => {
    navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.USER_DETAILS });
  };

  // Get default values for form
  const getDefaultValues = () => {
    return {
      organizationId: organizationId
    };
  };

  return (
    <div className="add-user p-4">
      <Toast ref={toast} position="top-right" />
      <Typography variant='h5' weight='semibold' className="mb-4">Add Employee</Typography>
        <DynamicForm
          schema={userFormSchema}
          onSubmit={handleSubmit}
          defaultValues={getDefaultValues()}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
        />
    </div>
  );
};

export default AddUser;
