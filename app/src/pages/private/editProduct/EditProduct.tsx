import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useSearch } from '@tanstack/react-router';
import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';

import organizationProductFormSchemaJson from '@/formSchemas/productForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import {
  useCategories,
  useSubcategoriesByCategory,
  useOrganizationProduct,
  useUpdateOrganizationProduct
} from '@/hooks/useCatalog';
import { OrganizationProduct, UpdateOrganizationProductRequest } from '@/types/catalog.types';
import { editProductRoute } from '@/routes/private/editProduct.route';
import { organizationItemCatalogRoute } from '@/routes/private/organizationItemCatalog.route';
import './EditProduct.css';
import Button from '@/components/ui/Button';
import { useOrganizationContext } from '@/context/OrganizationContext';

const EditOrganizationProduct: React.FC = () => {
  const navigate = useNavigate();
  // Type assertion to handle the search parameters
  const search = useSearch({ from: editProductRoute.id }) as { id: string };
  const id = typeof search.id === 'string' && !isNaN(Number(search.id)) ? Number(search.id) : 0;
  const toast = useRef<ToastRef>(null);

  const { organizationId } = useOrganizationContext();
  const safeOrgId = typeof organizationId === 'number' && !isNaN(organizationId) ? organizationId : 0;

  // State for selected category
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  // State for image deletion
  const [isImageDeleted, setIsImageDeleted] = useState(false);

  // Fetch organization product data
  const { data: organizationProductData, isLoading: isOrganizationProductLoading, isError: isOrganizationProductError } = useOrganizationProduct(safeOrgId, id);
  const organizationProduct = (organizationProductData?.data as (OrganizationProduct & { attributes?: string })) || undefined;

  // Handle error in fetching organization product data
  useEffect(() => {
    if (isOrganizationProductError) {
      toast.current?.showError('Failed to fetch organization product data');
      navigate({ to: organizationItemCatalogRoute.to });
    }
  }, [isOrganizationProductError, navigate]);

  // Fetch categories
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];

  // Compute valid selectedCategoryId for subcategory fetching
  const validCategoryId = typeof selectedCategoryId === 'number' && !isNaN(selectedCategoryId) ? selectedCategoryId : null;
  // Fetch subcategories based on selected category
  const { data: subcategoriesData } = useSubcategoriesByCategory(validCategoryId);
  const subcategories = subcategoriesData?.data || [];

  // Update organization product mutation
  const updateOrganizationProductMutation = useUpdateOrganizationProduct();

  // Set initial category ID when organization product data is loaded
  useEffect(() => {
    if (organizationProduct && typeof organizationProduct.categoryId === 'number' && !selectedCategoryId) {
      setSelectedCategoryId(organizationProduct.categoryId);
    }
  }, [organizationProduct, selectedCategoryId]);

  // Compute the form schema with updated category/subcategory options
  const organizationProductFormSchema: FormSchema = React.useMemo(() => {
    const schema: FormSchema = JSON.parse(JSON.stringify(organizationProductFormSchemaJson));
    const categoryField = schema.fields.find(field => field.name === 'categoryId');
    if (categoryField) {
      categoryField.options = categories.map(category => ({
        label: category.name,
        value: String(category.id),
      }));
    }
    const subcategoryField = schema.fields.find(field => field.name === 'subCategoryId');
    if (subcategoryField) {
      subcategoryField.options = subcategories.map(subcategory => ({
        label: subcategory.name,
        value: String(subcategory.id),
      }));
    }
    // Update submit button label
    const submitAction = schema.actions.find(action => action.id === 'submit');
    if (submitAction) {
      submitAction.label = 'Update Organization Product';
    }
    return schema;
  }, [categories, subcategories]);

  // Handle category change
  const handleCategoryChange = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
  };

  // Handle form field changes
  const handleFormFieldChange = (field: string, value: any) => {
    if (field === 'categoryId') {
      handleCategoryChange(typeof value === 'string' ? parseInt(value) : value);
    }
  };

  // Handle form submission
  const handleSubmit = async (data: any) => {
    if (!organizationProduct) return;
    try {
      // Convert attributes array to object, omitting empty keys/values
      let attributesJson = undefined;
      if (Array.isArray(data.attributes)) {
        const attrObj: Record<string, string> = {};
        data.attributes.forEach((item: any) => {
          if (item.key && item.value) attrObj[item.key] = item.value;
        });
        attributesJson = Object.keys(attrObj).length > 0 ? JSON.stringify(attrObj) : undefined;
      }

      // Prepare organization product data
      const organizationProductData: UpdateOrganizationProductRequest = {
        name: data.name,
        description: data.description,
        categoryId: typeof data.categoryId === 'string' ? parseInt(data.categoryId) : data.categoryId,
        subCategoryId: typeof data.subCategoryId === 'string' ? parseInt(data.subCategoryId) : data.subCategoryId,
        organizationId: safeOrgId,
        imageFile: data.imageFile || null,
        deleteImage: isImageDeleted,
        attributes: attributesJson,
      };
      // Update organization product
      await updateOrganizationProductMutation.mutateAsync({
        id: organizationProduct.id!,
        data: organizationProductData,
      });
      // Show success message
      toast.current?.showSuccess('Organization product updated successfully');
      // Navigate back to catalog
      navigate({ to: organizationItemCatalogRoute.to });
    } catch (error) {
      console.error('Error updating organization product:', error);
      // Show error message
      toast.current?.showError('Failed to update organization product');
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate({ to: organizationItemCatalogRoute.to });
  };

  // Prepare default values for the form
  const getDefaultValues = () => {
    if (!organizationProduct) return {};
    let attributesArray: { key: string; value: string }[] = [];
    if (organizationProduct.attributes) {
      try {
        const attrObj = typeof organizationProduct.attributes === 'string'
          ? JSON.parse(organizationProduct.attributes)
          : organizationProduct.attributes;
        if (attrObj && typeof attrObj === 'object') {
          attributesArray = Object.entries(attrObj).map(([key, value]) => ({
            key,
            value: String(value)
          }));
        }
      } catch (e) {
        // fallback: ignore attributes if parsing fails
      }
    }
    return {
      name: organizationProduct.name,
      description: organizationProduct.description || '',
      categoryId: organizationProduct.categoryId ? String(organizationProduct.categoryId) : null,
      subCategoryId: organizationProduct.subCategoryId ? String(organizationProduct.subCategoryId) : null,
      imageFile: null, // Start with no file selected for editing
      attributes: attributesArray,
    };
  };

  if (isOrganizationProductLoading) {
    return (
      <div className="edit-product p-4">
        <Card
          title="Edit Organization Product"
          variant="elevated"
          padding="large"
          className="max-w-3xl mx-auto"
        >
          <div className="flex justify-content-center">
            <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
          </div>
        </Card>
      </div>
    );
  }

  if (!organizationProduct) {
    return (
      <div className="edit-product p-4">
        <Card
          title="Edit Organization Product"
          variant="elevated"
          padding="large"
          className="max-w-3xl mx-auto"
        >
          <div className="text-center">
            <p>Organization product not found.</p>
            <Button
              variant="primary"
              onClick={handleCancel}
              className="mt-3"
            >
              Back to Catalog
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="edit-product p-4">
      <Toast ref={toast} position="top-right" />
      <Card
        title="Edit Organization Product"
        subtitle="Update organization product details"
        variant="elevated"
        padding="large"
        className="max-w-3xl mx-auto"
      >
        <DynamicForm
          key={organizationProduct?.id} // Force re-render when organization product changes
          schema={organizationProductFormSchema}
          onSubmit={handleSubmit}
          defaultValues={getDefaultValues()}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
          onFieldChange={handleFormFieldChange}
          fieldCallbacks={{
            imageFile: {
              onDeleteStateChange: setIsImageDeleted
            }
          }}
          fieldData={{
            imageFile: {
              existingImageUrl: organizationProduct?.imageUrl
            }
          }}
        />
      </Card>
    </div>
  );
};

export default EditOrganizationProduct;