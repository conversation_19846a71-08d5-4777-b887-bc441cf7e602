import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import { useNavigate, useSearch } from '@tanstack/react-router';
import React, { useEffect, useRef, useState } from 'react';

import type { FormSchema } from '@/components/form/DynamicForm';
import Button from '@/components/ui/Button';
import productFormSchemaJson from '@/formSchemas/productForm.json';
import {
  useCategories,
  useProduct,
  useSubcategoriesByCategory,
  useUpdateProduct
} from '@/hooks/useCatalog';
import { editProductCatalogRoute } from '@/routes/private/editProductCatalog.route';
import { productCatalogRoute } from '@/routes/private/productCatalog.route';
import { Product, UpdateProductRequest } from '@/types/catalog.types';
import './EditProductCatalog.css';

const EditProductCatalog: React.FC = () => {
  const navigate = useNavigate();
  // Type assertion to handle the search parameters
  const search = useSearch({ from: editProductCatalogRoute.id }) as { id: string };
  const id = typeof search.id === 'string' && !isNaN(Number(search.id)) ? Number(search.id) : 0;
  const toast = useRef<ToastRef>(null);

  // No need for organizationId or safeOrgId

  // State for selected category
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  // State for image deletion
  const [isImageDeleted, setIsImageDeleted] = useState(false);

  // Fetch product data
  const { data: productData, isLoading: isProductLoading, isError: isProductError } = useProduct(id);
  const product = (productData?.data as (Product & { attributes?: string })) || undefined;

  // Handle error in fetching product data
  useEffect(() => {
    if (isProductError) {
      toast.current?.showError('Failed to fetch product data');
      navigate({ to: productCatalogRoute.to });
    }
  }, [isProductError, navigate]);

  // Fetch categories
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];

  // Compute valid selectedCategoryId for subcategory fetching
  const validCategoryId = typeof selectedCategoryId === 'number' && !isNaN(selectedCategoryId) ? selectedCategoryId : null;
  // Fetch subcategories based on selected category
  const { data: subcategoriesData } = useSubcategoriesByCategory(validCategoryId);
  const subcategories = subcategoriesData?.data || [];

  // Update product mutation
  const updateProductMutation = useUpdateProduct();

  // Set initial category ID when product data is loaded
  useEffect(() => {
    if (product && typeof product.categoryId === 'number' && !selectedCategoryId) {
      setSelectedCategoryId(product.categoryId);
    }
  }, [product, selectedCategoryId]);

  // Compute the form schema with updated category/subcategory options
  const productFormSchema: FormSchema = React.useMemo(() => {
    const schema: FormSchema = JSON.parse(JSON.stringify(productFormSchemaJson));
    const categoryField = schema.fields.find(field => field.name === 'categoryId');
    if (categoryField) {
      categoryField.options = categories.map(category => ({
        label: category.name,
        value: String(category.id),
      }));
    }
    const subcategoryField = schema.fields.find(field => field.name === 'subCategoryId');
    if (subcategoryField) {
      subcategoryField.options = subcategories.map(subcategory => ({
        label: subcategory.name,
        value: String(subcategory.id),
      }));
    }
    // Update submit button label
    const submitAction = schema.actions.find(action => action.id === 'submit');
    if (submitAction) {
      submitAction.label = 'Update Product';
    }
    return schema;
  }, [categories, subcategories]);

  // Handle category change
  const handleCategoryChange = (categoryId: number) => {
    setSelectedCategoryId(categoryId);
  };

  // Handle form field changes
  const handleFormFieldChange = (field: string, value: any) => {
    if (field === 'categoryId') {
      handleCategoryChange(typeof value === 'string' ? parseInt(value) : value);
    }
  };

  // Handle form submission
  const handleSubmit = async (data: any) => {
    if (!product) return;
    try {
      // Convert attributes array to object, omitting empty keys/values
      let attributesJson = undefined;
      if (Array.isArray(data.attributes)) {
        const attrObj: Record<string, string> = {};
        data.attributes.forEach((item: any) => {
          if (item.key && item.value) attrObj[item.key] = item.value;
        });
        attributesJson = Object.keys(attrObj).length > 0 ? JSON.stringify(attrObj) : undefined;
      }

      // Prepare product data
      const productData: UpdateProductRequest = {
        name: data.name,
        description: data.description,
        categoryId: typeof data.categoryId === 'string' ? parseInt(data.categoryId) : data.categoryId,
        subCategoryId: typeof data.subCategoryId === 'string' ? parseInt(data.subCategoryId) : data.subCategoryId,
        // organizationId: safeOrgId, // Remove this line
        imageFile: data.imageFile || null,
        deleteImage: isImageDeleted,
        attributes: attributesJson,
      };
      // Update product
      await updateProductMutation.mutateAsync({
        id: product.id!,
        data: productData,
      });
      // Show success message
      toast.current?.showSuccess('Product updated successfully');
      // Navigate back to catalog
      navigate({ to: productCatalogRoute.to });
    } catch (error) {
      console.error('Error updating product:', error);
      // Show error message
      toast.current?.showError('Failed to update product');
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate({ to: productCatalogRoute.to });
  };

  // Prepare default values for the form
  const getDefaultValues = () => {
    if (!product) return {};
    let attributesArray: { key: string; value: string }[] = [];
    if (product.attributes) {
      try {
        const attrObj = typeof product.attributes === 'string'
          ? JSON.parse(product.attributes)
          : product.attributes;
        if (attrObj && typeof attrObj === 'object') {
          attributesArray = Object.entries(attrObj).map(([key, value]) => ({
            key,
            value: String(value)
          }));
        }
      } catch (e) {
        // fallback: ignore attributes if parsing fails
      }
    }
    return {
      name: product.name,
      description: product.description || '',
      categoryId: product.categoryId ? String(product.categoryId) : null,
      subCategoryId: product.subCategoryId ? String(product.subCategoryId) : null,
      imageFile: null, // Start with no file selected for editing
      attributes: attributesArray,
    };
  };

  if (isProductLoading) {
    return (
      <div className="edit-product p-4">
        <Card
          title="Edit Product"
          variant="elevated"
          padding="large"
          className="max-w-3xl mx-auto"
        >
          <div className="flex justify-content-center">
            <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
          </div>
        </Card>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="edit-product p-4">
        <Card
          title="Edit Product"
          variant="elevated"
          padding="large"
          className="max-w-3xl mx-auto"
        >
          <div className="text-center">
            <p>Product not found.</p>
            <Button
              variant="primary"
              onClick={handleCancel}
              className="mt-3"
            >
              Back to Catalog
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="edit-product p-4">
      <Toast ref={toast} position="top-right" />
      <Card
        title="Edit Product"
        subtitle="Update product details"
        variant="elevated"
        padding="large"
        className="max-w-3xl mx-auto"
      >
        <DynamicForm
          key={product?.id} // Force re-render when product changes
          schema={productFormSchema}
          onSubmit={handleSubmit}
          defaultValues={getDefaultValues()}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
          onFieldChange={handleFormFieldChange}
          fieldCallbacks={{
            imageFile: {
              onDeleteStateChange: setIsImageDeleted
            }
          }}
          fieldData={{
            imageFile: {
              existingImageUrl: product?.imageUrl
            }
          }}
        />
      </Card>
    </div>
  );
};

export default EditProductCatalog;