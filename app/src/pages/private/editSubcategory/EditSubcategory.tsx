import React, { useState, useRef, useEffect } from 'react';
import { useNavigate, useSearch } from '@tanstack/react-router';
import { editSubcategoryRoute } from '@/routes/private/editSubcategory.route';
import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';

import subcategoryFormSchemaJson from '@/formSchemas/subcategoryForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { useCategories, useSubcategories, useSubcategory, useUpdateSubcategory } from '@/hooks/useCatalog';
import { UpdateSubcategoryRequest } from '@/types/catalog.types';
import { organizationItemCatalogRoute } from '@/routes/private/organizationItemCatalog.route';
import './EditSubcategory.css';
import { useOrganizationContext } from '@/context/OrganizationContext';

const EditSubcategory: React.FC = () => {
  const navigate = useNavigate();
  // Type assertion to handle the search parameters
  const search = useSearch({ from: editSubcategoryRoute.id }) as { id: string };
  const id = parseInt(search.id);
  const toast = useRef<ToastRef>(null);

  const { organizationId } = useOrganizationContext();

  // State for form schema
  const [subcategoryFormSchema, setSubcategoryFormSchema] = useState<FormSchema>(subcategoryFormSchemaJson as FormSchema);

  // State to track if image is marked for deletion
  const [isImageDeleted, setIsImageDeleted] = useState<boolean>(false);

  // Validate id parameter on component mount
  useEffect(() => {
    if (!search.id || isNaN(id)) {
      toast.current?.showError('Valid Subcategory ID is required');
      navigate({ to: organizationItemCatalogRoute.to });
    }
  }, [search.id, id, navigate]);

  // Since we need categoryId to fetch subcategory, we'll get all subcategories first
  // and find the one we need
  const { data: allSubcategoriesData } = useSubcategories();
  const allSubcategories = allSubcategoriesData?.data || [];

  // Find the subcategory we're editing
  const subcategory = allSubcategories.find(sub => sub.id === id);
  const categoryId = subcategory?.categoryId;

  // Handle case when subcategory is not found
  useEffect(() => {
    if (allSubcategoriesData && !subcategory) {
      toast.current?.showError('Subcategory not found');
      navigate({ to: organizationItemCatalogRoute.to });
    }
  }, [allSubcategoriesData, subcategory, navigate]);

  // Fetch categories
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];

  // Update subcategory mutation
  const updateSubcategoryMutation = useUpdateSubcategory();

  // Update form schema with categories
  useEffect(() => {
    const updatedSchema = { ...subcategoryFormSchema };

    // Update category options
    const categoryField = updatedSchema.fields.find(field => field.name === 'categoryId');
    if (categoryField) {
      categoryField.options = categories.map(category => ({
        label: category.name,
        value: category.id,
      }));
    }

    setSubcategoryFormSchema(updatedSchema);
  }, [categories]);

  // Handle form submission
  const handleSubmit = async (data: any) => {
    if (!categoryId) {
      toast.current?.showError('Category information is missing');
      return;
    }

    try {
      const subcategoryData: UpdateSubcategoryRequest = {
        name: data.name,
        description: data.description,
        categoryId: typeof data.categoryId === 'string' ? parseInt(data.categoryId) : data.categoryId,
        organizationId: organizationId as number,
        deleteImage: isImageDeleted
      };

      // Only include imageFile if it's actually a File object (new upload)
      if (data.imageFile && data.imageFile instanceof File) {
        subcategoryData.imageFile = data.imageFile;
      }

      await updateSubcategoryMutation.mutateAsync({ categoryId, id, data: subcategoryData });

      // Show success toast and navigate back
      toast.current?.showSuccess('Subcategory updated successfully');
      navigate({ to: organizationItemCatalogRoute.to });
    } catch (error) {
      console.error('Error updating subcategory:', error);
      toast.current?.showError('Failed to update subcategory');
    }
  };

  // Handle cancel button
  const handleCancel = () => {
    navigate({ to: organizationItemCatalogRoute.to });
  };

  // Get default values for form
  const getDefaultValues = () => {
    if (!subcategory) return {};

    return {
      name: subcategory.name,
      description: subcategory.description || '',
      categoryId: subcategory.categoryId,
      // Don't set imageFile for existing images - file inputs should only receive File objects
      // The FileUpload component will handle displaying existing images via initialValue prop
    };
  };

  return (
    <div className="edit-subcategory p-4">
      <Toast ref={toast} position="top-right" />

      <Card
        title="Edit Subcategory"
        subtitle="Update subcategory details"
        variant="elevated"
        padding="large"
        className="max-w-3xl mx-auto"
      >
        {!subcategory ? (
          <div className="loading-indicator">Loading subcategory data...</div>
        ) : (
          <DynamicForm
            schema={subcategoryFormSchema}
            onSubmit={handleSubmit}
            defaultValues={getDefaultValues()}
            className="mt-4"
            buttonHandlers={{
              cancel: handleCancel
            }}
            fieldCallbacks={{
              imageFile: {
                onDeleteStateChange: setIsImageDeleted
              }
            }}
            fieldData={{
              imageFile: {
                existingImageUrl: subcategory?.imageUrl
              }
            }}
          />
        )}
      </Card>
    </div>
  );
};

export default EditSubcategory;
