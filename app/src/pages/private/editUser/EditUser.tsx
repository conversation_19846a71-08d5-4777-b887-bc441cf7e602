import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { DynamicForm } from '@/components/form/DynamicForm';
import Card from '@/components/ui/Card/Card';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';

import editUserFormSchemaJson from '@/formSchemas/editUserForm.json';
import type { FormSchema } from '@/components/form/DynamicForm';
import { useUser, useUpdateUser } from '@/hooks/useUser';
import { useDepartments } from '@/hooks/useDepartment';
import { useDesignations } from '@/hooks/useDesignation';
import { UserUpdateRequest, Role } from '@/types/api/user';
import { editUserRoute } from '@/routes/private/editUser.route';
import { ROUTES } from '@/constants/routes.constant';
import './EditUser.css';
import { useOrganizationContext } from '@/context/OrganizationContext';
import Typography from '@/components/ui/Typography';
import { useLocations } from '@/hooks/useLocation';

const EditUser: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);

  // Get user ID from route params
  const { id } = editUserRoute.useSearch();
  const userId = parseInt(id);

  // State for form schema
  const [editUserFormSchema, setEditUserFormSchema] = useState<FormSchema>(editUserFormSchemaJson as FormSchema);

  // Fetch user data
  const { organizationId } = useOrganizationContext();
  const { data: user, isLoading: userLoading, error: userError } = useUser(userId, organizationId);

  // Fetch departments and designations for dropdowns
  const { data: departmentsData } = useDepartments(organizationId);
  const { data: designationsData } = useDesignations(organizationId);
  const { data : locationData } = useLocations(organizationId);

  const departments = departmentsData || [];
  const designations = designationsData?.data || [];
  const locations = locationData || [];

  // Update user mutation
  const updateUserMutation = useUpdateUser();

  // Update form schema with department and designation options
  useEffect(() => {
    const updatedSchema = { ...editUserFormSchema };

    // Update department options
    const departmentField = updatedSchema.fields.find(field => field.name === 'departmentId');
    if (departmentField && departmentField.type === 'select') {
      departmentField.options = departments.map(dept => ({
        label: dept.name,
        value: dept.id?.toString() || ''
      }));
    }

    // Update designation options
    const designationField = updatedSchema.fields.find(field => field.name === 'designationId');
    if (designationField && designationField.type === 'select') {
      designationField.options = designations.map(designation => ({
        label: designation.name,
        value: designation.id.toString()
      }));
    }

    // Update location options
    const locationField = updatedSchema.fields.find(field => field.name === 'locationId');
    if (locationField && locationField.type === 'select') {
      locationField.options = locations.map(location => ({
        label: location.name,
        value: location.id.toString()
      }));
    }

    setEditUserFormSchema(updatedSchema);
  }, [departments, designations, locations]);

  // Handle form submission - only send changed fields
  const handleSubmit = async (data: any) => {
    if (!user) return;

    try {
      const updateData: UserUpdateRequest = {};

      // Only include fields that have changed and are not empty
      if (data.name && data.name.trim() !== '' && data.name !== user.name) {
        updateData.name = data.name;
      }
      if (data.email && data.email.trim() !== '' && data.email !== user.email) {
        updateData.email = data.email;
      }
      if (data.phoneNumber && data.phoneNumber.trim() !== '' && data.phoneNumber !== user.phoneNumber) {
        updateData.phoneNumber = data.phoneNumber;
      }
      if (data.departmentId && data.departmentId !== user.departmentId?.toString()) {
        updateData.departmentId = parseInt(data.departmentId);
      }
      if (data.designationId && data.designationId !== user.designationId?.toString()) {
        updateData.designationId = parseInt(data.designationId);
      }
      if (data.locationId && data.locationId !== user.locationId?.toString()) {
        updateData.locationId = parseInt(data.locationId);
      }
      if (data.employeeId !== undefined && data.employeeId !== user.employeeId) {
        updateData.employeeId = data.employeeId || undefined;
      }
      if (data.joiningDate !== undefined) {
        const newJoiningDate = data.joiningDate ? data.joiningDate.toISOString().split('T')[0] : undefined;
        const currentJoiningDate = user.joiningDate ? user.joiningDate.split('T')[0] : undefined;
        if (newJoiningDate !== currentJoiningDate) {
          updateData.joiningDate = newJoiningDate;
        }
      }
      if (data.reportingManagerId !== undefined && data.reportingManagerId !== user.reportingManagerId) {
        updateData.reportingManagerId = data.reportingManagerId || undefined;
      }

      // Only include password if it was provided
      if (data.password && data.password.trim() !== '') {
        updateData.password = data.password;
      }

      // Always include organizationId for backend compatibility
      updateData.organizationId = organizationId;

      await updateUserMutation.mutateAsync({ id: userId, data: updateData, organizationId });

      // Show success toast and navigate back
      toast.current?.showSuccess('User updated successfully');
      navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.USER_DETAILS });
    } catch (error) {
      toast.current?.showError('Failed to update user');
    }
  };

  // Handle cancel button
  const handleCancel = () => {
    navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.USER_DETAILS });
  };

  // Get default values for form
  const getDefaultValues = () => {
    if (!user) return {};

    return {
      name: user.name || '',
      email: user.email || '',
      phoneNumber: user.phoneNumber || '',
      role: user.role || Role.ROLE_USER,
      departmentId: user.departmentId?.toString() || '',
      designationId: user.designationId?.toString() || '',
      locationId: user.locationId?.toString() || '',
      employeeId: user.employeeId || '',
      joiningDate: user.joiningDate ? new Date(user.joiningDate) : null,
      reportingManagerId: user.reportingManagerId || null,
      organizationId: organizationId
    };
  };

  // Show loading state
  if (userLoading) {
    return (
      <div className="edit-user p-4">
        <Card
          title="Edit User"
          subtitle="Loading user details..."
          variant="elevated"
          padding="large"
          className="max-w-3xl mx-auto"
        >
          <div className="loading-indicator">
            <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
            <span className="ml-2">Loading user details...</span>
          </div>
        </Card>
      </div>
    );
  }

  // Show error state
  if (userError || !user) {
    return (
      <div className="edit-user p-4">
        <Toast ref={toast} position="top-right" />
        <Card
          title="Edit User"
          subtitle="Error loading user"
          variant="elevated"
          padding="large"
          className="max-w-3xl mx-auto"
        >
          <div className="error-message">
            <i className="pi pi-exclamation-triangle" style={{ fontSize: '2rem', color: 'var(--red-500)' }}></i>
            <span className="ml-2">Failed to load user details. Please try again.</span>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="edit-user p-4">
      <Toast ref={toast} position="top-right" />
      <Typography variant='h5' weight='semibold' className="mb-4">Edit Employee</Typography>
        <DynamicForm
          key={user.id} // Force re-render when user changes
          schema={editUserFormSchema}
          onSubmit={handleSubmit}
          defaultValues={getDefaultValues()}
          className="mt-4"
          buttonHandlers={{
            cancel: handleCancel
          }}
          fieldData={{
            reportingManagerId: {
              excludeUserId: user.id // Exclude current user from reporting manager search
            }
          }}
        />
    </div>
  );
};

export default EditUser;
