import { DynamicForm, FormSchema } from '@/components/form/DynamicForm';
import Button from '@/components/ui/Button';
import Modal from '@/components/ui/Modal/Modal';
import { ToastRef } from '@/components/ui/Toast/Toast';
import Typography from '@/components/ui/Typography';
import organizationEditFormSchemaJson from '@/formSchemas/organizationEditForm.json';
import { useCurrentOrganization, useCurrentOrganizationPrimaryLocation, useUpdateOrganization } from '@/hooks/useOrganization';
import '@pages/private/organizationDetails/OrganizationDetails.css';
import { Message } from 'primereact/message';
import { ProgressSpinner } from 'primereact/progressspinner';
import React, { useRef, useState } from 'react';

const editOrganizationFormSchemaJson = organizationEditFormSchemaJson as FormSchema;

const OrganizationDetails: React.FC = () => {
    // Fetch organization data from API
    const { data: organization, isLoading, error, isError } = useCurrentOrganization();

    const toast = useRef<ToastRef>(null);
    const widthBeforeColon = "120px"; // Width for labels in the form

    // Fetch primary location data separately
    const {
        data: primaryLocation,
        isLoading: isPrimaryLocationLoading,
        error: primaryLocationError
    } = useCurrentOrganizationPrimaryLocation();

    const updateOrganizationMutation = useUpdateOrganization();

    const [isEditModalOpen, setIsEditModalOpen] = useState<boolean>(false);
    const [editFormError, setEditFormError] = useState<string | null>(null);

    const handleEditOrganizationSubmit = async (formData: any) => {
        try{
            setEditFormError(null);
            if(organization){
                await updateOrganizationMutation.mutateAsync({
                    id: organization.id,
                    data: formData
                });
                toast?.current?.showSuccess("Organization updated successfully");
            } else {
                toast?.current?.showError("Organization not found");
            }
            setIsEditModalOpen(false);
        } catch (error) {
            console.error("Error updating organization:", error);
            setEditFormError("Failed to update organization. Please try again.");
        }
    }

    // Loading state
    if (isLoading) {
        return (
            <div className="organization_details p-4">
            <Typography variant='h5' weight='semibold' className="mb-4">Organization Details</Typography>
                    <div className="flex justify-content-center align-items-center" style={{ minHeight: '200px' }}>
                        <ProgressSpinner />
                    </div>
            </div>
        );
    }

    // Error state
    if (isError || !organization) {
        return (
            <div className="organization_details p-4">
            <Typography variant='h5' weight='semibold' className="mb-4">Organization Details</Typography>
                    <Message
                        severity="error"
                        text={error?.message || "Failed to load organization details"}
                        className="w-full"
                    />
            </div>
        );
    }

    return (
        <div className="organization_details p-4">
            <Typography variant='h5' weight='semibold' className="mb-4">Organization Details</Typography>
                <div className="grid">
                    {/* Basic Information and System Information side by side */}
                    <div className="col-8">
                        <div className="grid">
                            <div className="col-12 md:col-6">
                                <div className='flex align-items-center gap-3 mb-3'>
                                <Typography weight='semibold' variant="h6">Basic Information</Typography>
                                <Button
                                    variant='outline'
                                    onClick={() => setIsEditModalOpen(true)}
                                >
                                    <i className="pi pi-pencil"></i>
                                </Button>
                                </div>
                                <div className="flex flex-column gap-3">
                                    <div className="flex align-items-center">
                                        <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>Name</label>
                                        <span className="mx-1">:</span>
                                        <div className="ml-2">{organization.name}</div>
                                    </div>
                                    {organization.description &&
                                        <div className="flex align-items-center">
                                            <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>Description</label>
                                            <span className="mx-1">:</span>
                                            <div className="ml-2">{organization.description}</div>
                                        </div>
                                    }
                                    {organization.gstin &&
                                        <div className="flex align-items-center">
                                            <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>GST Number</label>
                                            <span className="mx-1">:</span>
                                            <div className="ml-2">{organization.gstin}</div>
                                        </div>

                                    }
                                </div>
                            </div>

                            <div className="col-12 md:col-6">
                                <Typography weight='semibold' variant="h6" className="mb-3">System Information</Typography>
                                <div className="flex flex-column gap-3">
                                    <div className="flex align-items-center">
                                        <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>Organization ID</label>
                                        <span className="mx-1">:</span>
                                        <div className="ml-2">{organization.id}</div>
                                    </div>
                                    <div className="flex align-items-center">
                                        <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>Status</label>
                                        <span className="mx-1">:</span>
                                        <div>
                                            <span className={`status-badge ml-2 ${organization.enabled ? 'active' : 'inactive'}`}>
                                                {organization.enabled ? 'Enabled' : 'Disabled'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Edit organization modal */}
                    <Modal
                        visible={isEditModalOpen}
                        onHide={() => setIsEditModalOpen(false)}
                        header="Edit Organization"
                        modalProps={{ style: { width: '30vw' } }}
                    >
                        <DynamicForm
                            schema={editOrganizationFormSchemaJson}
                            defaultValues={organization || {}}
                            buttonHandlers={{
                                cancel: () => setIsEditModalOpen(false)
                            }}
                            onSubmit={handleEditOrganizationSubmit}
                        />
                    </Modal>

                    {/* Primary Location */}
                    <div className="col-12">
                        <Typography weight='semibold' variant="h6" className="mb-3">Primary Location</Typography>
                            {isPrimaryLocationLoading ? (
                                <div className="flex justify-content-center align-items-center" style={{ minHeight: '100px' }}>
                                    <ProgressSpinner />
                                </div>
                            ) : primaryLocationError ? (
                                <div className="flex align-items-center justify-content-center" style={{ minHeight: '100px' }}>
                                    <span className="text-500">Primary location not set</span>
                                </div>
                            ) : primaryLocation ? (
                                <div className="flex flex-column gap-2">
                                    <div className="flex align-items-center">
                                        <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>Location Name</label>
                                        <span className="mx-1">:</span>
                                        <div className="ml-2 overflow-wrap-break-word white-space-normal">{primaryLocation.name}</div>
                                    </div>
                                    <div className="flex align-items-center">
                                        <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>Timezone</label>
                                        <span className="mx-1">:</span>
                                        <div className="ml-2 overflow-wrap-break-word white-space-normal">{primaryLocation.timezone}</div>
                                    </div>
                                    <div className="flex align-items-center">
                                        <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>Address Line 1</label>
                                        <span className="mx-1">:</span>
                                        <div className="ml-2 overflow-wrap-break-word white-space-normal">{primaryLocation.address.addressLine1}</div>
                                    </div>
                                    {primaryLocation.address.addressLine2 && (
                                        <div className="flex align-items-center">
                                            <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>Address Line 2</label>
                                            <span className="mx-1">:</span>
                                            <div className="ml-2 overflow-wrap-break-word white-space-normal">{primaryLocation.address.addressLine2}</div>
                                        </div>
                                    )}
                                    <div className="flex align-items-center">
                                        <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>City</label>
                                        <span className="mx-1">:</span>
                                        <div className="ml-2 overflow-wrap-break-word white-space-normal">{primaryLocation.address.city}</div>
                                    </div>
                                    <div className="flex align-items-center">
                                        <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>State</label>
                                        <span className="mx-1">:</span>
                                        <div className="ml-2 overflow-wrap-break-word white-space-normal">{primaryLocation.address.state}</div>
                                    </div>
                                    <div className="flex align-items-center">
                                        <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>Country</label>
                                        <span className="mx-1">:</span>
                                        <div className="ml-2 overflow-wrap-break-word white-space-normal">{primaryLocation.address.countryCode}</div>
                                    </div>
                                    {primaryLocation.address.postalCode && (
                                        <div className="flex align-items-center">
                                            <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>Postal Code</label>
                                            <span className="mx-1">:</span>
                                            <div className="ml-2 overflow-wrap-break-word white-space-normal">{primaryLocation.address.postalCode}</div>
                                        </div>
                                    )}
                                    {primaryLocation.address.latitude && primaryLocation.address.longitude && (
                                        <div className="flex align-items-center">
                                            <label className="text-sm font-semibold" style={{ width: widthBeforeColon }}>Coordinates</label>
                                            <span className="mx-1">:</span>
                                            <div className="ml-2 overflow-wrap-break-word white-space-normal">{primaryLocation.address.latitude}, {primaryLocation.address.longitude}</div>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="flex align-items-center justify-content-center" style={{ minHeight: '100px' }}>
                                    <span className="text-500">Primary location not set</span>
                                </div>
                            )}
                    </div>
                </div>

        </div>
    );
};

export default OrganizationDetails;