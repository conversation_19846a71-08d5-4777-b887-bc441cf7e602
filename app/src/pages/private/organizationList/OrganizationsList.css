@import url('../../../variables.css');

.organizations-list {
  .status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: var(--font-semibold);
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }

  .status-badge.active {
    background-color: var(--green-100);
    color: var(--green-800);
    border: 1px solid var(--green-200);
  }

  .status-badge.inactive {
    background-color: var(--red-100);
    color: var(--red-800);
    border: 1px solid var(--red-200);
  }
}

@media screen and (max-width: 768px) {
  .organizations-list .confirmation-content {
    flex-direction: column;
    text-align: center;
  }
  
  .organizations-list .confirmation-content i {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
}
