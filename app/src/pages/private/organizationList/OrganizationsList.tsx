import React, { useState, useRef } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { DataGrid, ColumnConfig } from '@/components/ui/DataGrid/DataGrid';
import Button from '@/components/ui/Button/Button';
import Modal from '@/components/ui/Modal/Modal';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import Typography from '@/components/ui/Typography';
import { OrganizationShortResponse } from '@/types/organization.types';
import { useAllOrganizationsShort, useDeleteOrganization } from '@/hooks/useOrganization';
import './OrganizationsList.css';
import { ROUTES } from '@/constants/routes.constant';

const OrganizationsList: React.FC = () => {
  const navigate = useNavigate();
  const toast = useRef<ToastRef>(null);
  
  const { data: organizations, isLoading } = useAllOrganizationsShort();
  const deleteOrganization = useDeleteOrganization();

  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [organizationToDelete, setOrganizationToDelete] = useState<OrganizationShortResponse | null>(null);
  const [totalRecords, setTotalRecords] = useState<number>(0);
  const [first, setFirst] = useState<number>(0);
  const [rows, setRows] = useState<number>(10);

  React.useEffect(() => {
    if (organizations) {
      setTotalRecords(organizations.length);
    }
  }, [organizations]);

  const handleAdd = () => {
    navigate({ to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.ADD_ORGANIZATION });
  };

  const handleEdit = (organization: OrganizationShortResponse) => {
    navigate({
      to: ROUTES.PRIVATE.APP + ROUTES.PRIVATE.EDIT_ORGANIZATION,
      search: { id: organization.id.toString() }
    });
  };

  const handleDeleteClick = (organization: OrganizationShortResponse) => {
    setOrganizationToDelete(organization);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (organizationToDelete) {
      try {
        await deleteOrganization.mutateAsync(organizationToDelete.id);
        toast.current?.showSuccess('Organization deleted successfully');
        setIsDeleteModalOpen(false);
      } catch (error) {
        toast.current?.showError('Failed to delete organization');
      }
    }
  };

  const columns: ColumnConfig[] = [
    {
      field: 'name',
      header: 'Organization Name',
      sortable: true
    },
    {
      field: 'description',
      header: 'Description',
      sortable: true
    },
    {
      field: 'organizationAdminEmail',
      header: 'Admin Email',
      sortable: true
    },
    {
      field: 'enabled',
      header: 'Status',
      sortable: true,
      body: (rowData: OrganizationShortResponse) => (
        <span className={`status-badge ${rowData.enabled ? 'active' : 'inactive'}`}>
          {rowData.enabled ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      field: 'actions',
      header: 'Actions',
      sortable: false,
      body: (rowData: OrganizationShortResponse) => (
        <div className="flex gap-2 justify-content-center">
          {/* <Button
            icon="pi pi-pencil"
            variant="outline"
            size="small"
            onClick={() => handleEdit(rowData)}
            aria-label="Edit"
          /> */}
          <Button
            icon="pi pi-trash"
            variant="outline"
            size="small"
            onClick={() => handleDeleteClick(rowData)}
            aria-label="Delete"
            className="p-button-danger"
          />
        </div>
      )
    }
  ];

  return (
    <div className="organizations-list p-4">
      <Toast ref={toast} position="top-right" />
      <Typography variant='h5' weight='semibold' className="mb-4">Organization Management</Typography>
      
      <div className="flex justify-content-end mb-3">
        <Button
          size='small'
          variant="primary"
          leftIcon={<i className="pi pi-plus"></i>}
          onClick={handleAdd}
        >
          Add Organization
        </Button>
      </div>
    {/* TODO: apply pagination */}
      <DataGrid
        value={organizations || []}
        columns={columns}
        loading={isLoading}
        showGridLines={true}
        stripedRows={true}
        totalRecords={totalRecords}
        rows={rows}
        onPage={(e) => setFirst(e.first)}
        paginator={true}
        rowsPerPageOptions={[10, 25, 50]}
      />

      <Modal
        visible={isDeleteModalOpen}
        onHide={() => setIsDeleteModalOpen(false)}
        header="Confirm Delete"
        modalProps={{ style: { width: '30vw' } }}
      >
        <div className="confirmation-content">
          <i className="pi pi-exclamation-triangle mr-3" style={{ fontSize: '2rem', color: 'var(--orange-500)' }}></i>
          <span>
            Are you sure you want to delete organization <strong>{organizationToDelete?.name}</strong>?
            This action cannot be undone.
          </span>
        </div>
        <div className="flex justify-content-end gap-2 mt-4">
          <Button
            variant="outline"
            onClick={() => setIsDeleteModalOpen(false)}
          >
            Cancel
          </Button>
          <Button
            variant="primary"
            className="p-button-danger"
            onClick={handleDeleteConfirm}
          >
            Delete
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default OrganizationsList;
