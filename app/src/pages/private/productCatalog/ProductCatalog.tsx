import { DynamicForm } from '@/components/form/DynamicForm';
import Button from '@/components/ui/Button/Button';
import { ColumnConfig, DataGrid } from '@/components/ui/DataGrid/DataGrid';
import Modal from '@/components/ui/Modal/Modal';
import Toast, { ToastRef } from '@/components/ui/Toast/Toast';
import Typography from '@/components/ui/Typography';
import { useAuth } from '@/hooks/useAuth';
import { useCategories, useCreateCategory, useCreateSubcategory, useDeleteProduct, useProducts, useSubcategories } from '@/hooks/useCatalog';
import { addProductCatalogRoute } from '@/routes/private/addProductCatalog.route';
import { editProductCatalogRoute } from '@/routes/private/editProductCatalog.route';
import { Role } from '@/types/api/user';
import { useNavigate } from '@tanstack/react-router';
import { Dropdown } from 'primereact/dropdown';
import React, { useMemo, useRef, useState } from 'react';
import './ProductCatalog.css';

const ProductCatalog: React.FC = () => {
  const toast = useRef<ToastRef>(null);
  const navigate = useNavigate();
  const { userRole } = useAuth();
  // organizationId is no longer required for hooks

  // State for modals
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [showSubcategoryModal, setShowSubcategoryModal] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<any>(null);

  // Filter state
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<number | null>(null);

  // Fetch products
  const { data: productsData, isLoading } = useProducts({ page: 1, limit: 20, sort: 'name,asc' });
  const products = productsData?.data || [];
  // const productsTotal = productsData?.total || 0;

  // Fetch categories and subcategories
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];
  const categoryOptions = categories.map((cat: any) => ({ label: cat.name, value: cat.id }));

  const { data: subcategoriesData } = useSubcategories();
  const subcategories = subcategoriesData?.data || [];
  const subcategoryOptions = subcategories.map((sub: any) => ({ label: sub.name, value: sub.id }));

  // Mutations
  const createCategory = useCreateCategory();
  const createSubcategory = useCreateSubcategory();
  const deleteProduct = useDeleteProduct();

  // Handle delete click
  const handleDeleteClick = (product: any) => {
    setProductToDelete(product);
    setIsDeleteModalOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (productToDelete) {
      try {
        await deleteProduct.mutateAsync(productToDelete.id);
        toast.current?.showSuccess('Product deleted successfully');
        setIsDeleteModalOpen(false);
        setProductToDelete(null);
      } catch (error) {
        toast.current?.showError('Failed to delete product');
      }
    }
  };

  const handleEdit = (product: any) => {
    console.log(product.id.toString())
    navigate({
      to: editProductCatalogRoute.to,
      search: { id: product.id.toString() }
    });
  };

  // Table columns
  const columns: ColumnConfig[] = [
    { field: 'name', header: 'Product Name', sortable: true },
    { field: 'categoryName', header: 'Category', sortable: true },
    { field: 'subcategoryName', header: 'Subcategory', sortable: true },
    { field: 'description', header: 'Description' },
    ...(userRole === Role.ROLE_SUPER_ADMIN ? [{
      field: 'actions',
      header: 'Actions',
      sortable: false,
      body: (rowData: any) => (
        <div className="flex gap-2 justify-content-center">
          <Button
            icon="pi pi-pencil"
            variant="outline"
            size="small"
            onClick={() => handleEdit(rowData)}
            aria-label="Edit"
          />
          <Button
            icon="pi pi-trash"
            variant="outline"
            size="small"
            onClick={() => handleDeleteClick(rowData)}
            aria-label="Delete"
            className="p-button-danger"
          />
        </div>
      )
    }] : [])
  ];

  // Filter products in-memory
  const filteredProducts = useMemo(() => {
    return products.filter((product: any) => {
      if(typeof selectedCategory !== "number"){
        setSelectedCategory(null);
      }
      if(typeof selectedSubcategory !== "number"){
        setSelectedSubcategory(null);
      }
      const matchCategory = selectedCategory ? product.categoryId === selectedCategory : true;
      const matchSubcategory = selectedSubcategory ? product.subCategoryId === selectedSubcategory : true;
      return matchCategory && matchSubcategory;
    });
  }, [products, selectedCategory, selectedSubcategory]);

  // Form schemas
  // TODO: move form schema to JSON
  const categoryFormSchema = {
    fields: [
      { name: 'name', type: 'text' as const, label: 'Category Name', validation: { required: true }, placeholder: "Enter category name",
      icon: "pi-tag", },
      { name: 'description', type: 'text' as const, label: 'Description', placeholder: "Enter category description",
      icon: "pi-align-left", validation: { required: false } },
    ],
    actions: [
      { id: 'submit', type: 'submit' as const, label: 'Add Category' },
    ],
  };

  const subcategoryFormSchema = {
    fields: [
      {
        name: 'name', type: 'text' as const, label: 'Subcategory Name', placeholder: "Enter subcategory name",
        icon: "pi-tag", validation: { required: true }
      },
      {
        name: 'description', type: 'text' as const, label: 'Description', placeholder: "Enter subcategory description",
        icon: "pi-align-left", validation: { required: false }
      },
      { name: 'categoryId', type: 'select' as const, label: 'Category', options: categoryOptions, validation: { required: true } },
    ],
    actions: [
      { id: 'submit', type: 'submit' as const, label: 'Add Subcategory' },
    ],
  };

  // Handlers
  const handleAddCategory = async (data: any) => {
    try {
      await createCategory.mutateAsync(data);
      toast.current?.showSuccess('Category added successfully');
      setShowCategoryModal(false);
    } catch {
      toast.current?.showError('Failed to add category');
    }
  };

  const handleAddSubcategory = async (data: any) => {
    try {
      await createSubcategory.mutateAsync(data);
      toast.current?.showSuccess('Subcategory added successfully');
      setShowSubcategoryModal(false);
    } catch {
      toast.current?.showError('Failed to add subcategory');
    }
  };

  return (
    <div className="product-item-catalog p-4">
      <Toast ref={toast} position="top-right" />
      <Typography variant="h5" weight="semibold" className="mb-4">
        Product Catalogue
      </Typography>
      <div className='flex justify-content-between gap-2'>
        <div className="filter-container mb-4 flex gap-2">
          <Dropdown
            value={selectedCategory}
            options={[{ label: 'All Categories', value: null }, ...categoryOptions]}
            onChange={e => {
              setSelectedCategory(e.value);
              setSelectedSubcategory(null); // Reset subcategory filter when category changes
            }}
            placeholder="Filter by Category"
            className="category-filter"
            showClear
          />
          <Dropdown
            value={selectedSubcategory}
            options={[
              { label: 'All Subcategories', value: null },
              ...(
                selectedCategory
                  ? subcategories.filter((sub: any) => sub.categoryId === selectedCategory).map((sub: any) => ({ label: sub.name, value: sub.id }))
                  : subcategoryOptions
              )
            ]}
            onChange={e => setSelectedSubcategory(e.value)}
            placeholder="Filter by Subcategory"
            className="subcategory-filter"
            showClear
            disabled={!selectedCategory && subcategoryOptions.length === 0}
          />
        </div>
        <div className="flex gap-2 mb-4">
          {userRole === Role.ROLE_SUPER_ADMIN &&
            <>
              <Button variant="primary" size="small" onClick={() => setShowCategoryModal(true)}>
                Add Category
              </Button>
              <Button variant="primary" size="small" onClick={() => setShowSubcategoryModal(true)}>
                Add Subcategory
              </Button>
              <Button variant="primary" size="small" onClick={() => navigate({ to: addProductCatalogRoute.to })}>
                Add Product
              </Button>
            </>
          }
        </div>
      </div>
      <DataGrid
        value={filteredProducts}
        columns={columns}
        totalRecords={filteredProducts.length}
        loading={isLoading}
        rows={20}
        rowsPerPageOptions={[20, 50, 100]}
        showGridLines={true}
        stripedRows={true}
        emptyMessage="No products found"
      />
      {/* Add Category Modal */}
      <Modal
        visible={showCategoryModal}
        onHide={() => setShowCategoryModal(false)}
        header="Add Category"
        footerButtons={[]}
      >
        <DynamicForm
          schema={categoryFormSchema}
          onSubmit={handleAddCategory}
        />
      </Modal>
      {/* Add Subcategory Modal */}
      <Modal
        visible={showSubcategoryModal}
        onHide={() => setShowSubcategoryModal(false)}
        header="Add Subcategory"
        footerButtons={[]}
      >
        <DynamicForm
          schema={subcategoryFormSchema}
          onSubmit={handleAddSubcategory}
        />
      </Modal>
      {/* Delete Confirmation Modal */}
      <Modal
        visible={isDeleteModalOpen}
        onHide={() => {
          setIsDeleteModalOpen(false);
          setProductToDelete(null);
        }}
        header="Confirm Delete"
        footerButtons={[
          {
            label: 'Cancel',
            icon: 'pi pi-times',
            variant: 'outline',
            onClick: () => {
              setIsDeleteModalOpen(false);
              setProductToDelete(null);
            },
          },
          {
            label: 'Delete',
            icon: 'pi pi-trash',
            variant: 'danger',
            onClick: handleDeleteConfirm,
          },
        ]}
      >
        <p>Are you sure you want to delete this product?</p>
        {productToDelete && (
          <div className="mt-3">
            <p><strong>Name:</strong> {productToDelete.name}</p>
            <p><strong>Category:</strong> {productToDelete.categoryName}</p>
            <p><strong>Subcategory:</strong> {productToDelete.subcategoryName}</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ProductCatalog;