@import url('../../../variables.css');

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-approved {
  background-color: #d1fae5;
  color: #065f46;
}

.status-rejected {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-cancelled {
  background-color: #f3f4f6;
  color: #374151;
}

/* Products modal styling */

.products-list {
  max-height: 400px;
  overflow-y: auto;
}

.product-item {
  background-color: #f9fafb;
  border-color: #e5e7eb !important;
}

.p-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link{
  color: var(--color-primary);
  border-color: var(--color-primary);
}