@import url('../../../variables.css');

/* Status badges */
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.enabled {
  background-color: var(--green-100);
  color: var(--green-800);
  border: 1px solid var(--green-200);
}

.status-badge.disabled {
  background-color: var(--red-100);
  color: var(--red-800);
  border: 1px solid var(--red-200);
}

/* Role badges */
.role-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: var(--font-medium);
  background-color: var(--blue-100);
  color: var(--blue-800);
  border: 1px solid var(--blue-200);
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .user_details .card {
    padding: var(--card-padding-mobile);
  }
  
  .confirmation-content {
    flex-direction: column;
    text-align: center;
  }
  
  .confirmation-content i {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
}
