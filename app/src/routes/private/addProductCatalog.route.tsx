import { ROUTES } from "@/constants/routes.constant";
import AddProductCatalog from "@/pages/private/addProductCatalog/AddProductCatalog";
import { appRoute } from "@/routes/private/app.route";
import { createRoute } from "@tanstack/react-router";

export const addProductCatalogRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.ADD_PRODUCT,
  component: AddProductCatalog,
});
