import { createRoute, Navigate, useLocation } from "@tanstack/react-router";
import { rootRoute } from "@/routes/root.route";
import { ROUTES } from "@/constants/routes.constant";
import { MainLayout } from "@/layouts/MainLayout";
import { useAuth } from "@/hooks/useAuth";
import { loginRoute } from "../public/login.route";

export const ProtectedMainLayout: React.FC = () => {
    const { isAuthenticated, isLoading } = useAuth();
    const location = useLocation();
    if (!isLoading && !isAuthenticated) {
        return <Navigate to={loginRoute.to} />;
    }

    // Check if we're at exactly the /app route (no sub-path)
    const isExactAppPath = location.pathname === ROUTES.PRIVATE.APP;
    if (isExactAppPath) {
        return <Navigate to={`${ROUTES.PRIVATE.APP}${ROUTES.PRIVATE.DASHBOARD}`} />;
    }

    return <MainLayout />;
};

export const appRoute = createRoute({
    getParentRoute: () => rootRoute,
    path: ROUTES.PRIVATE.APP,
    component: ProtectedMainLayout,
});