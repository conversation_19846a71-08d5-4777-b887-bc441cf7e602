import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import CreatePurchaseRequest from "@/pages/private/createPurchaseRequest/CreatePurchaseRequest";

export const createPurchaseRequestRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.NEW_PURCHASE_REQUEST,
  component: CreatePurchaseRequest,
});
