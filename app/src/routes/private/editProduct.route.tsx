import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import EditProduct from "@/pages/private/editProduct/EditProduct";

export const editProductRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.EDIT_ORG_PRODUCT,
  component: EditProduct,
  validateSearch: (search: Record<string, unknown>) => {
    // Ensure id is always a string and is required
    if (typeof search.id !== 'string' || !search.id) {
      throw new Error('Product ID is required and must be a string');
    }
    return {
      id: search.id,
    };
  },
});
