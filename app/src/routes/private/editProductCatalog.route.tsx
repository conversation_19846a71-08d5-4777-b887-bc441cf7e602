import { ROUTES } from "@/constants/routes.constant";
import EditProductCatalog from "@/pages/private/editProductCatalog/EditProductCatalog";
import { appRoute } from "@/routes/private/app.route";
import { createRoute } from "@tanstack/react-router";

export const editProductCatalogRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.EDIT_PRODUCT,
  component: EditProductCatalog,
  validateSearch: (search: Record<string, unknown>) => {
    // Ensure id is always a string and is required
    if (typeof search.id !== 'string' || !search.id) {
      throw new Error('Product ID is required and must be a string');
    }
    return {
      id: search.id,
    };
  },
});
