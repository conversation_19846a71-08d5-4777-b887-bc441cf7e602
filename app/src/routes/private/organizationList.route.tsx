import { ROUTES } from "@/constants/routes.constant";
import OrganizationsList from "@/pages/private/organizationList/OrganizationsList";
import { appRoute } from "@/routes/private/app.route";
import { createRoute } from "@tanstack/react-router";

export const organizationListRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.ORGANIZATION_LIST,
  component: OrganizationsList,
});
