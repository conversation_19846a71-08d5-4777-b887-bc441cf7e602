import { createRoute } from "@tanstack/react-router";
import { ROUTES } from "@/constants/routes.constant";
import { appRoute } from "@/routes/private/app.route";
import PurchaseRequestManagement from "@/pages/private/purchaseRequestManagement/PurchaseRequestManagement";

export const purchaseRequestManagementRoute = createRoute({
  getParentRoute: () => appRoute,
  path: ROUTES.PRIVATE.PURCHASE_REQUESTS,
  component: PurchaseRequestManagement,
});
