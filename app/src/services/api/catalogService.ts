/**
 * Service for handling catalog-related API operations
 * Integrates with the backend API endpoints for categories, subcategories, and products
 */

import { apiClient } from './client';
import {
  Category,
  Subcategory,
  Product,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  CreateSubcategoryRequest,
  UpdateSubcategoryRequest,
  CreateProductRequest,
  UpdateProductRequest,
  OrganizationProduct,
  CreateOrganizationProductRequest,
  UpdateOrganizationProductRequest,
} from '@/types/catalog.types';
import { ApiResponse, PaginatedResponse, QueryParams } from '@/types/api/common';

export async function getCategories(
  params?: QueryParams
): Promise<PaginatedResponse<Category>> {
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    const response = await apiClient.get<Category[]>(CATEGORIES_URL);

    // Since backend doesn't support pagination for categories, we'll simulate it
    let categories = response || [];

    // Apply sorting if provided
    if (params?.sort) {
      const [field, order] = params.sort.split(',');
      categories.sort((a: any, b: any) => {
        const aValue = a[field] || '';
        const bValue = b[field] || '';
        if (order === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    } else {
      // Default sort by name
      categories.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
    }

    // Calculate pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    // Get paginated data
    const paginatedData = categories.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: categories.length,
      page: page,
      limit: limit,
      totalPages: Math.ceil(categories.length / limit)
    };
  } catch (error) {
    console.error('Error fetching categories:', error);
    throw error;
  }
}

export async function getCategoryById(
  id: number
): Promise<ApiResponse<Category>> {
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    const response = await apiClient.get<Category>(`${CATEGORIES_URL}/${id}`);
    return {
      data: response,
      message: 'Category retrieved successfully',
      status: 200
    };
  } catch (error) {
    console.error(`Error fetching category with ID ${id}:`, error);
    throw error;
  }
}

export async function createCategory(
  data: CreateCategoryRequest
): Promise<ApiResponse<Category>> {
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('description', data.description);

    if (data.imageFile) {
      formData.append('imageFile', data.imageFile);
    }

    const response = await apiClient.post<Category>(CATEGORIES_URL, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return {
      data: response,
      message: 'Category created successfully',
      status: 201
    };
  } catch (error) {
    console.error('Error creating category:', error);
    throw error;
  }
}

export async function updateCategory(
  id: number,
  data: UpdateCategoryRequest
): Promise<ApiResponse<Category>> {
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('description', data.description);

    // Add deleteImage flag - defaults to false if not provided
    formData.append('deleteImage', (data.deleteImage || false).toString());

    if (data.imageFile) {
      formData.append('imageFile', data.imageFile);
    }

    const response = await apiClient.put<Category>(`${CATEGORIES_URL}/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return {
      data: response,
      message: 'Category updated successfully',
      status: 200
    };
  } catch (error) {
    console.error(`Error updating category with ID ${id}:`, error);
    throw error;
  }
}

export async function deleteCategory(
  id: number
): Promise<ApiResponse<void>> {
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    await apiClient.delete<void>(`${CATEGORIES_URL}/${id}`);
    return {
      data: undefined,
      message: 'Category deleted successfully',
      status: 204
    };
  } catch (error) {
    console.error(`Error deleting category with ID ${id}:`, error);
    throw error;
  }
}

export async function getSubcategories(
  params?: QueryParams
): Promise<PaginatedResponse<Subcategory>> {
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    // If category_id is provided in filter, get subcategories for that category
    if (params?.filter?.category_id) {
      const categoryId = params.filter.category_id;
      const numericCategoryId = typeof categoryId === 'string' ? parseInt(categoryId) : categoryId;
      if (typeof numericCategoryId === 'number' && !isNaN(numericCategoryId)) {
        return getSubcategoriesByCategoryId(numericCategoryId, params);
      } else {
        console.error('Invalid category_id in filter:', categoryId);
        // Fall through to get all subcategories
      }
    }

    // Use the new endpoint to get all subcategories
    const subcategoriesResponse = await apiClient.get<Subcategory[]>(
      `${CATEGORIES_URL}/sub-categories`
    );

    let allSubcategories = subcategoriesResponse || [];

    // Get category names for UI display
    const categoriesResponse = await apiClient.get<Category[]>(CATEGORIES_URL);
    const categories = categoriesResponse || [];

    // Add category names to subcategories
    allSubcategories = allSubcategories.map(sub => {
      const category = categories.find(cat => cat.id === sub.categoryId);
      return {
        ...sub,
        categoryName: category?.name || 'Unknown'
      };
    });

    // Apply sorting
    if (params?.sort) {
      const [field, order] = params.sort.split(',');
      allSubcategories.sort((a: any, b: any) => {
        const aValue = a[field] || '';
        const bValue = b[field] || '';
        if (order === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    } else {
      // Default sort by name
      allSubcategories.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
    }

    // Calculate pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const paginatedData = allSubcategories.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: allSubcategories.length,
      page: page,
      limit: limit,
      totalPages: Math.ceil(allSubcategories.length / limit)
    };
  } catch (error) {
    console.error('Error fetching subcategories:', error);
    throw error;
  }
}

export async function getSubcategoriesByCategoryId(
  categoryId: number,
  params?: QueryParams
): Promise<PaginatedResponse<Subcategory>> {
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    // Validate categoryId
    if (typeof categoryId !== 'number' || isNaN(categoryId)) {
      throw new Error(`Invalid categoryId: ${categoryId}. Expected a number.`);
    }

    const response = await apiClient.get<Subcategory[]>(
      `${CATEGORIES_URL}/${categoryId}/sub-categories`
    );

    let subcategories = response || [];

    // Get category name for UI display
    try {
      const categoryResponse = await apiClient.get<Category>(`${CATEGORIES_URL}/${categoryId}`);
      subcategories = subcategories.map(sub => ({
        ...sub,
        categoryName: categoryResponse.name
      }));
    } catch (error) {
      console.warn(`Error fetching category name for category ${categoryId}:`, error);
    }

    // Apply sorting
    if (params?.sort) {
      const [field, order] = params.sort.split(',');
      subcategories.sort((a: any, b: any) => {
        const aValue = a[field] || '';
        const bValue = b[field] || '';
        if (order === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    } else {
      // Default sort by name
      subcategories.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
    }

    // Calculate pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const paginatedData = subcategories.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: subcategories.length,
      page: page,
      limit: limit,
      totalPages: Math.ceil(subcategories.length / limit)
    };
  } catch (error) {
    console.error(`Error fetching subcategories for category ID ${categoryId}:`, error);
    throw error;
  }
}

export async function getSubcategoryById(
  categoryId: number,
  id: number
): Promise<ApiResponse<Subcategory>> {
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    const response = await apiClient.get<Subcategory>(`${CATEGORIES_URL}/${categoryId}/sub-categories/${id}`);
    return {
      data: response,
      message: 'Subcategory retrieved successfully',
      status: 200
    };
  } catch (error) {
    console.error(`Error fetching subcategory with ID ${id}:`, error);
    throw error;
  }
}

export async function createSubcategory(
  data: CreateSubcategoryRequest
): Promise<ApiResponse<Subcategory>> {
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('description', data.description);
    formData.append('categoryId', data.categoryId.toString());

    if (data.imageFile) {
      formData.append('imageFile', data.imageFile);
    }

    const response = await apiClient.post<Subcategory>(
      `${CATEGORIES_URL}/${data.categoryId}/sub-categories`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return {
      data: response,
      message: 'Subcategory created successfully',
      status: 201
    };
  } catch (error) {
    console.error('Error creating subcategory:', error);
    throw error;
  }
}

export async function updateSubcategory(
  categoryId: number,
  id: number,
  data: UpdateSubcategoryRequest
): Promise<ApiResponse<Subcategory>> {
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('description', data.description);
    formData.append('categoryId', data.categoryId.toString());

    // Add deleteImage flag - defaults to false if not provided
    formData.append('deleteImage', (data.deleteImage || false).toString());

    if (data.imageFile) {
      formData.append('imageFile', data.imageFile);
    }

    const response = await apiClient.put<Subcategory>(
      `${CATEGORIES_URL}/${categoryId}/sub-categories/${id}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return {
      data: response,
      message: 'Subcategory updated successfully',
      status: 200
    };
  } catch (error) {
    console.error(`Error updating subcategory with ID ${id}:`, error);
    throw error;
  }
}

export async function deleteSubcategory(
  categoryId: number,
  id: number
): Promise<ApiResponse<void>> {
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    await apiClient.delete<void>(`${CATEGORIES_URL}/${categoryId}/sub-categories/${id}`);
    return {
      data: undefined,
      message: 'Subcategory deleted successfully',
      status: 204
    };
  } catch (error) {
    console.error(`Error deleting subcategory with ID ${id}:`, error);
    throw error;
  }
}

export async function getProducts(
  params?: QueryParams
): Promise<PaginatedResponse<Product>> {
  const PRODUCTS_URL = `/v1/products`;
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    const response = await apiClient.get<Product[]>(PRODUCTS_URL);
    let products = response || [];

    // Enhance products with category and subcategory names for UI display
    const enhancedProducts = await Promise.all(
      products.map(async (product) => {
        let categoryName = '';
        let subcategoryName = '';

        try {
          // Get category info
          if (product.categoryId) {
            const categoryResponse = await apiClient.get<Category>(`${CATEGORIES_URL}/${product.categoryId}`);
            categoryName = categoryResponse.name;
          }

          // Get subcategory info
          if (product.subCategoryId && product.categoryId) {
            const subcategoryResponse = await apiClient.get<Subcategory>(
              `${CATEGORIES_URL}/${product.categoryId}/sub-categories/${product.subCategoryId}`
            );
            subcategoryName = subcategoryResponse.name;
          }
        } catch (error) {
          console.warn(`Error fetching category/subcategory info for product ${product.id}:`, error);
        }

        return {
          ...product,
          categoryName,
          subcategoryName
        };
      })
    );

    // Apply filtering if provided
    let filteredProducts = enhancedProducts;
    if (params?.filter) {
      const filters = params.filter;

      // Filter by subcategory_id if provided (null means "All Subcategories", so skip filtering)
      if (filters.subcategory_id !== null && filters.subcategory_id !== undefined) {
        filteredProducts = filteredProducts.filter(
          product => product.subCategoryId === filters.subcategory_id
        );
      }

      // Filter by category_id if provided (null means "All Categories", so skip filtering)
      if (filters.category_id !== null && filters.category_id !== undefined) {
        filteredProducts = filteredProducts.filter(
          product => product.categoryId === filters.category_id
        );
      }

      // Filter by name if provided (case-insensitive partial match)
      if (filters.name) {
        const searchTerm = filters.name.toString().toLowerCase();
        filteredProducts = filteredProducts.filter(
          product => product.name.toLowerCase().includes(searchTerm)
        );
      }
    }

    // Apply sorting
    if (params?.sort) {
      const [field, order] = params.sort.split(',');
      filteredProducts.sort((a: any, b: any) => {
        const aValue = a[field] || '';
        const bValue = b[field] || '';
        if (order === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    } else {
      // Default sort by name
      filteredProducts.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
    }

    // Calculate pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;

    const paginatedData = filteredProducts.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: filteredProducts.length,
      page: page,
      limit: limit,
      totalPages: Math.ceil(filteredProducts.length / limit)
    };
  } catch (error) {
    console.error('Error fetching products:', error);
    throw error;
  }
}

export async function getProductById(
  id: number
): Promise<ApiResponse<Product>> {
  const PRODUCTS_URL = `/v1/products`;
  try {
    const response = await apiClient.get<Product>(`${PRODUCTS_URL}/${id}`);
    return {
      data: response,
      message: 'Product retrieved successfully',
      status: 200
    };
  } catch (error) {
    console.error(`Error fetching product with ID ${id}:`, error);
    throw error;
  }
}

export async function createProduct(
  data: CreateProductRequest
): Promise<ApiResponse<Product>> {
  const PRODUCTS_URL = `/v1/products`;
  try {
    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('description', data.description);

    formData.append('categoryId', data.categoryId.toString());
    formData.append('subCategoryId', data.subCategoryId.toString());

    // Append image file if provided
    if (data.imageFile) {
      formData.append('imageFile', data.imageFile);
    }

    // Append attributes JSON string if present
    if (data.attributes) {
      formData.append('attributes', data.attributes);
    }

    const response = await apiClient.post<Product>(PRODUCTS_URL, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return {
      data: response,
      message: 'Product created successfully',
      status: 201
    };
  } catch (error) {
    console.error('Error creating product:', error);
    throw error;
  }
}

export async function updateProduct(
  id: number,
  data: UpdateProductRequest
): Promise<ApiResponse<Product>> {
  const PRODUCTS_URL = `/v1/products`;
  try {
    // Create FormData for multipart upload
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('description', data.description);

    formData.append('categoryId', data.categoryId.toString());
    formData.append('subCategoryId', data.subCategoryId.toString());

    // Append image file if provided
    if (data.imageFile) {
      formData.append('imageFile', data.imageFile);
    }

    // Append attributes JSON string if present
    if (data.attributes) {
      formData.append('attributes', data.attributes);
    }

    // Handle image deletion
    if (data.deleteImage) {
      formData.append('deleteImage', 'true');
    }

    const response = await apiClient.put<Product>(`${PRODUCTS_URL}/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return {
      data: response,
      message: 'Product updated successfully',
      status: 200
    };
  } catch (error) {
    console.error(`Error updating product with ID ${id}:`, error);
    throw error;
  }
}

export async function deleteProduct(
  id: number
): Promise<ApiResponse<void>> {
  const PRODUCTS_URL = `/v1/products`;
  try {
    await apiClient.delete<void>(`${PRODUCTS_URL}/${id}`);
    return {
      data: undefined,
      message: 'Product deleted successfully',
      status: 204
    };
  } catch (error) {
    console.error(`Error deleting product with ID ${id}:`, error);
    throw error;
  }
}

// Get all organization products (with optional filtering, sorting, pagination)
export async function getOrganizationProducts(
  organizationId: number,
  params?: QueryParams
): Promise<PaginatedResponse<OrganizationProduct>> {
  const ORG_PRODUCTS_URL = `/v1/products/org-products`;
  const CATEGORIES_URL = `/v1/product-categories`;
  try {
    const response = await apiClient.get<OrganizationProduct[]>(`${ORG_PRODUCTS_URL}?organizationId=${organizationId}`);
    let products = response || [];

    // Enhance products with category and subcategory names for UI display
    const enhancedProducts = await Promise.all(
      products.map(async (product) => {
        let categoryName = '';
        let subcategoryName = '';

        try {
          // Get category info
          if (product.categoryId) {
            const categoryResponse = await apiClient.get<Category>(`${CATEGORIES_URL}/${product.categoryId}`);
            categoryName = categoryResponse.name;
          }

          // Get subcategory info
          if (product.subCategoryId && product.categoryId) {
            const subcategoryResponse = await apiClient.get<Subcategory>(
              `${CATEGORIES_URL}/${product.categoryId}/sub-categories/${product.subCategoryId}`
            );
            subcategoryName = subcategoryResponse.name;
          }
        } catch (error) {
          console.warn(`Error fetching category/subcategory info for org product ${product.id}:`, error);
        }

        return {
          ...product,
          categoryName,
          subcategoryName
        };
      })
    );

    // Filtering
    let filteredProducts = enhancedProducts;
    if (params?.filter) {
      const filters = params.filter;
      if (filters.subcategory_id !== null && filters.subcategory_id !== undefined) {
        filteredProducts = filteredProducts.filter(
          product => product.subCategoryId === filters.subcategory_id
        );
      }
      if (filters.category_id !== null && filters.category_id !== undefined) {
        filteredProducts = filteredProducts.filter(
          product => product.categoryId === filters.category_id
        );
      }
      if (filters.name) {
        const searchTerm = filters.name.toString().toLowerCase();
        filteredProducts = filteredProducts.filter(
          product => product.name.toLowerCase().includes(searchTerm)
        );
      }
    }

    // Sorting
    if (params?.sort) {
      const [field, order] = params.sort.split(',');
      filteredProducts.sort((a: any, b: any) => {
        const aValue = a[field] || '';
        const bValue = b[field] || '';
        if (order === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
    } else {
      filteredProducts.sort((a, b) => (a.name || '').localeCompare(b.name || ''));
    }

    // Pagination
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedData = filteredProducts.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: filteredProducts.length,
      page: page,
      limit: limit,
      totalPages: Math.ceil(filteredProducts.length / limit)
    };
  } catch (error) {
    console.error('Error fetching organization products:', error);
    throw error;
  }
}

// Get organization product by ID
export async function getOrganizationProductById(
  organizationId: number,
  id: number
): Promise<ApiResponse<OrganizationProduct>> {
  const ORG_PRODUCT_URL = `/v1/products/org-product/${id}`;
  try {
    const response = await apiClient.get<OrganizationProduct>(`${ORG_PRODUCT_URL}?organizationId=${organizationId}`);
    return {
      data: response,
      message: 'Organization product retrieved successfully',
      status: 200
    };
  } catch (error) {
    console.error(`Error fetching organization product with ID ${id}:`, error);
    throw error;
  }
}

// Create organization product
export async function createOrganizationProduct(
  data: CreateOrganizationProductRequest
): Promise<ApiResponse<OrganizationProduct>> {
  const ORG_PRODUCT_URL = `/v1/products/org-product`;
  try {
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('description', data.description);
    formData.append('organizationId', data.organizationId.toString());
    formData.append('categoryId', data.categoryId.toString());
    formData.append('subCategoryId', data.subCategoryId.toString());
    if (data.imageFile) {
      formData.append('imageFile', data.imageFile);
    }
    if (data.attributes) {
      formData.append('attributes', data.attributes);
    }
    const response = await apiClient.post<OrganizationProduct>(
      `${ORG_PRODUCT_URL}?organizationId=${data.organizationId}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return {
      data: response,
      message: 'Organization product created successfully',
      status: 201
    };
  } catch (error) {
    console.error('Error creating organization product:', error);
    throw error;
  }
}

// Update organization product
export async function updateOrganizationProduct(
  id: number,
  data: UpdateOrganizationProductRequest
): Promise<ApiResponse<OrganizationProduct>> {
  const ORG_PRODUCT_URL = `/v1/products/org-product/${id}`;
  try {
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('description', data.description);
    formData.append('organizationId', data.organizationId.toString());
    formData.append('categoryId', data.categoryId.toString());
    formData.append('subCategoryId', data.subCategoryId.toString());
    if (data.imageFile) {
      formData.append('imageFile', data.imageFile);
    }
    if (data.attributes) {
      formData.append('attributes', data.attributes);
    }
    if (data.deleteImage) {
      formData.append('deleteImage', 'true');
    }
    const response = await apiClient.put<OrganizationProduct>(
      `${ORG_PRODUCT_URL}?organizationId=${data.organizationId}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return {
      data: response,
      message: 'Organization product updated successfully',
      status: 200
    };
  } catch (error) {
    console.error(`Error updating organization product with ID ${id}:`, error);
    throw error;
  }
}

// Delete organization product
export async function deleteOrganizationProduct(
  organizationId: number,
  id: number
): Promise<ApiResponse<void>> {
  const ORG_PRODUCT_URL = `/v1/products/org-product/${id}`;
  try {
    await apiClient.delete<void>(`${ORG_PRODUCT_URL}?organizationId=${organizationId}`);
    return {
      data: undefined,
      message: 'Organization product deleted successfully',
      status: 204
    };
  } catch (error) {
    console.error(`Error deleting organization product with ID ${id}:`, error);
    throw error;
  }
}