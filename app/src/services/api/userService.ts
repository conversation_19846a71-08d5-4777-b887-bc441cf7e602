import { apiClient } from './client';
import {
  UserResponse,
  UserCreateRequest,
  UserUpdateRequest,
  LoginRequest,
  LoginResponse,
} from '@/types/api/user';
import { ApiResponse, QueryParams } from '@/types/api/common';

export class UserService {
  private static BASE_URL = '/v1/organization';

  /**
   * Get all users with pagination
   * @param params Query parameters for pagination, sorting, and filtering
   * @returns Promise with paginated user data
   */
  static async getUsers(organizationId: number, params?: QueryParams): Promise<UserResponse[]> {
    try {
      // Backend returns Page<UserResponse>, we'll extract the content
      const response = await apiClient.get<any>(`${this.BASE_URL}/${organizationId}/users`, { params });
      return response.content || response; // Handle both paginated and direct array responses
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  /**
   * Get user by ID
   * @param id User ID
   * @returns Promise with user data
   */
  static async getUserById(id: number, organizationId: number): Promise<UserResponse> {
    try {
      return await apiClient.get<UserResponse>(`${this.BASE_URL}/${organizationId}/users/${id}`);
    } catch (error) {
      console.error(`Error fetching user with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Create a new user
   * @param data User creation data
   * @returns Promise with created user data
   */
  static async createUser(data: UserCreateRequest, organizationId: number): Promise<UserResponse> {
    try {
      return await apiClient.post<UserResponse>(`${this.BASE_URL}/${organizationId}/users`, data);
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Update an existing user
   * @param id User ID
   * @param data User update data
   * @returns Promise with updated user data
   */
  static async updateUser(id: number, data: UserUpdateRequest, organizationId: number): Promise<UserResponse> {
    try {
      return await apiClient.put<UserResponse>(`${this.BASE_URL}/${organizationId}/users/${id}`, data);
    } catch (error) {
      console.error(`Error updating user with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Delete a user (soft delete)
   * @param id User ID
   * @returns Promise with void
   */
  static async deleteUser(id: number, organizationId: number): Promise<void> {
    try {
      await apiClient.delete<void>(`${this.BASE_URL}/${organizationId}/users/${id}`);
    } catch (error) {
      console.error(`Error deleting user with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Enable a user
   * @param id User ID
   * @returns Promise with updated user data
   */
  static async enableUser(id: number, organizationId: number): Promise<UserResponse> {
    try {
      return await apiClient.patch<UserResponse>(`${this.BASE_URL}/${organizationId}/users/${id}/enable`, {});
    } catch (error) {
      console.error(`Error enabling user with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Disable a user
   * @param id User ID
   * @returns Promise with updated user data
   */
  static async disableUser(id: number, organizationId: number): Promise<UserResponse> {
    try {
      return await apiClient.patch<UserResponse>(`${this.BASE_URL}/${organizationId}/users/${id}/disable`, {});
    } catch (error) {
      console.error(`Error disabling user with ID ${id}:`, error);
      throw error;
    }
  }

  /**
   * Search users
   * @param searchTerm Search term
   * @param role Role filter
   * @param enabled Enabled filter
   * @param params Additional query parameters
   * @returns Promise with search results
   */
  static async searchUsers(
    organizationId?: number,
    searchTerm?: string,
    role?: string,
    enabled?: boolean,
    params?: QueryParams
  ): Promise<UserResponse[]> {
    try {
      const searchParams = {
        ...params,
        searchTerm,
        role,
        enabled,
      };
      const response = await apiClient.get<any>(`${this.BASE_URL}/${organizationId}/users/search`, { params: searchParams });
      return response.content || response;
    } catch (error) {
      console.error('Error searching users:', error);
      throw error;
    }
  }

  // Legacy methods for backward compatibility
  static async login(data: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    return apiClient.post<ApiResponse<LoginResponse>>(`/v1/auth/login`, data);
  }

  static async logout(): Promise<void> {
    localStorage.removeItem('auth_token');
  }
}