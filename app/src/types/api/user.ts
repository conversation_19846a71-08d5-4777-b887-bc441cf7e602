// User response from backend (matches UserResponse.java)
export interface UserResponse {
  id: number;
  email: string;
  name: string;
  role: Role;
  enabled: boolean;
  phoneNumber: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
  // Additional fields from User entity
  departmentId?: number;
  departmentName?: string;
  designationId?: number;
  designationName?: string;
  locationId?: number;
  organizationId?: number;
  employeeId?: string;
  joiningDate?: string;
  terminationDate?: string;
  reportingManagerId?: number;
  reportingManagerName?: string;
}

// Backend Role enum (matches Role.java)
export enum Role {
  ROLE_USER = 'ROLE_USER',
  ROLE_ORG_ADMIN = 'ROLE_ORG_ADMIN',
  ROLE_SUPER_ADMIN = 'ROLE_SUPER_ADMIN',
}

// Role labels mapping
export const RoleLabels: Record<Role, string> = {
  [Role.ROLE_USER]: 'Employee',
  [Role.ROLE_ORG_ADMIN]: 'Organization Admin',
  [Role.ROLE_SUPER_ADMIN]: 'Super Admin',
};

// Create user request (matches UserCreateRequest.java)
export interface UserCreateRequest {
  email: string;
  password: string;
  name: string;
  phoneNumber: string;
  organizationId: number;
  departmentId: number;
  designationId: number;
  locationId: number;
  employeeId?: string;
  joiningDate?: string;
  reportingManagerId?: number;
}

// Update user request (matches UserUpdateRequest.java)
export interface UserUpdateRequest {
  email?: string;
  password?: string;
  name?: string;
  phoneNumber?: string;
  organizationId?: number;
  departmentId?: number;
  designationId?: number;
  locationId?: number;
  enabled?: boolean;
  employeeId?: string;
  joiningDate?: string;
  reportingManagerId?: number;
}

// Legacy interfaces for backward compatibility
export interface User extends UserResponse {}

export enum UserRole {
  ADMIN = 'ROLE_ADMIN',
  USER = 'ROLE_USER',
  GUEST = 'ROLE_USER',
}

export interface CreateUserRequest extends UserCreateRequest {}
export interface UpdateUserRequest extends UserUpdateRequest {}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: User;
}