/**
 * Address DTO matching backend AddressDTO structure
 */
export interface AddressDTO {
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string;
  countryCode: string; // ISO 3166-1 alpha-2 format (e.g., "US", "GB")
  postalCode?: string;
  latitude?: number;
  longitude?: number;
}

/**
 * Location DTO matching backend LocationDTO structure
 */
export interface LocationDTO {
  id: number;
  name: string;
  timezone: string;
  organizationId: number;
  address: AddressDTO;
  primary: boolean; // Backend sends "primary" to frontend
}

/**
 * Legacy Location interface for backward compatibility
 * @deprecated Use LocationDTO instead
 */
export interface Location {
  id: string;
  name: string;
  address_line1: string;
  address_line2?: string | null;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_primary: boolean;
  organization_id: string;

  // Audit fields
  created_by_ip: string;
  created_by_user: string;
  created_date: string;
  last_modified_by_ip: string;
  last_modified_by_user: string;
  last_modified_date: string;
  version: number;
  active: boolean;
}

/**
 * Request object for creating a new location (matches backend LocationDTO)
 */
export interface CreateLocationRequest {
  name: string;
  timezone: string;
  organizationId: number;
  address: AddressDTO;
  primary?: boolean;
}

/**
 * Request object for updating a location (matches backend LocationDTO)
 */
export interface UpdateLocationRequest {
  name?: string;
  timezone?: string;
  organizationId?: number;
  address?: AddressDTO;
  primary?: boolean;
}

/**
 * Legacy request interfaces for backward compatibility
 * @deprecated Use CreateLocationRequest/UpdateLocationRequest with new structure
 */
export interface LegacyCreateLocationRequest {
  name: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  is_primary: boolean;
  organization_id?: string; // Optional, can be determined by the system
}

export interface LegacyUpdateLocationRequest {
  name?: string;
  address_line1?: string;
  address_line2?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  is_primary?: boolean;
}
