/**
 * Organization interface based on PostgreSQL schema (Legacy - for backward compatibility)
 */
export interface Organization {
    id: number;
    created_by_ip: string;
    created_by_user: string;
    created_date: string;
    last_modified_by_ip: string;
    last_modified_by_user: string;
    last_modified_date: string;
    version: number;
    active: boolean;
    address_line1: string;
    address_line2: string | null;
    address_city: string;
    address_country_code: string;
    address_latitude: number | null;
    address_longitude: number | null;
    address_postal_code: string;
    address_state: string;
    description: string;
    logo_file_name: string | null;
    logo_file_path: string | null;
    deleted: boolean;
    name: string;
    gstin?: string; // GST Number for tax identification
}

/**
 * Organization detail response from backend API
 * Matches OrganizationDetailResponse DTO from backend
 */
export interface OrganizationDetailResponse {
    id: number;
    name: string;
    description: string;
    enabled: boolean;
    logoFileId: string | null;
    primaryLocation?: LocationDTO | null;
    gstin?: string;
}

/**
 * Organization short response from backend API
 * Matches OrganizationShortResponse DTO from backend
 */
export interface OrganizationShortResponse {
    id: number;
    name: string;
    enabled: boolean;
    description: string;
    logoFileId: string | null;
    gstin?: string;
}

/**
 * Location DTO for organization primary location
 * Matches LocationDTO from backend
 */
export interface LocationDTO {
    id: number;
    name: string;
    timezone: string;
    organizationId: number;
    address: AddressDTO;
    primary: boolean; // Backend sends "primary" to frontend
}

/**
 * Address DTO for location address information
 * Matches AddressDTO from backend
 */
export interface AddressDTO {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    state: string;
    countryCode: string;
    postalCode?: string;
    latitude?: number;
    longitude?: number;
}

/**
 * Organization admin creation request
 */
export interface OrganizationAdminCreate {
    name: string;
    email: string;
    phoneNumber: string;
    gstin?: string;
}

/**
 * Request object for creating a new organization
 */
export interface CreateOrganizationRequest {
    name: string;
    description?: string;
    enabled?: boolean;
    logoFileId?: string;
    organizationAdmin: OrganizationAdminCreate;
    gstin?: string;
}

/**
 * Request object for updating an organization
 */
export interface UpdateOrganizationRequest {
    name: string;
    description?: string;
    enabled?: boolean;
    logoFileId?: string;
    gstin?: string;
}
