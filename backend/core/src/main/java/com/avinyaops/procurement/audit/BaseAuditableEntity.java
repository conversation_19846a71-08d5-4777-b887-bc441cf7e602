package com.avinyaops.procurement.audit;

import java.io.Serializable;
import java.time.Instant;

import org.hibernate.annotations.SQLRestriction;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.Embedded;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.AttributeOverride;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@MappedSuperclass
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseAuditableEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @CreatedBy
    @Embedded
    @AttributeOverrides({
            @AttributeOverride(name = "loggedUser", column = @Column(name = "created_by_user")),
            @AttributeOverride(name = "clientIpAddress", column = @Column(name = "created_by_ip"))
    })
    private AuditorDetails createdBy;

    @CreatedDate
    @Column(name = "created_date", updatable = false)
    private Instant createdDate;

    @LastModifiedBy
    @Embedded
    @AttributeOverrides({
            @AttributeOverride(name = "loggedUser", column = @Column(name = "last_modified_by_user")),
            @AttributeOverride(name = "clientIpAddress", column = @Column(name = "last_modified_by_ip"))
    })
    private AuditorDetails lastModifiedBy;

    @LastModifiedDate
    @Column(name = "last_modified_date")
    private Instant lastModifiedDate;

    @Embedded
    @AttributeOverrides({
            @AttributeOverride(name = "loggedUser", column = @Column(name = "deleted_by_user")),
            @AttributeOverride(name = "clientIpAddress", column = @Column(name = "deleted_by_ip"))
    })
    private AuditorDetails deletedBy;

    @Column(name = "deleted_date")
    private Instant deletedDate;

    //TODO: fix soft deletion audit details
    /**
     * Soft delete the entity by setting deleted date and auditor
     * 
     * @param deletedBy the auditor who performed the deletion
     */
    public void softDelete(AuditorDetails deletedBy) {
        this.deletedDate = Instant.now();
        this.deletedBy = deletedBy;
    }

    /**
     * Soft delete the entity (deletedBy will be set by current context if
     * available)
     */
    public void softDelete() {
        this.deletedDate = Instant.now();
        // deletedBy should be set manually or through a service layer
    }

    /**
     * Check if the entity is soft deleted
     * 
     * @return true if entity is deleted, false otherwise
     */
    public boolean isDeleted() {
        return this.deletedDate != null;
    }

    /**
     * Check if the entity is active (not deleted)
     * 
     * @return true if entity is active, false if deleted
     */
    public boolean isActive() {
        return this.deletedDate == null;
    }

    // /**
    //  * Restore a soft deleted entity
    //  */
    // public void restore() {
    //     this.deletedDate = null;
    //     this.deletedBy = null;
    // }
}