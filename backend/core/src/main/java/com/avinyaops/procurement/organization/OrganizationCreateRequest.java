package com.avinyaops.procurement.organization;

import com.avinyaops.procurement.user.OrganizationAdminCreate;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrganizationCreateRequest {
    @NotBlank(message = "Organization name is required")
    @Size(max = 100, message = "Organization name must be less than 100 characters")
    private String name;

    @Size(max = 500, message = "Description must be less than 500 characters")
    private String description;

    @NotNull(message = "Organization admin details are required")
    private OrganizationAdminCreate organizationAdmin;

    private String logoFileId;

    @Builder.Default
    private boolean enabled = true;

    private String gstin;
}
