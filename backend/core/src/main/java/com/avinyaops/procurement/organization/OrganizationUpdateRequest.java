package com.avinyaops.procurement.organization;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrganizationUpdateRequest {
    @NotBlank(message = "Organization name is required")
    @Size(max = 100, message = "Organization name must be less than 100 characters")
    private String name;

    @Size(max = 500, message = "Description must be less than 500 characters")
    private String description;

    @Builder.Default
    private boolean enabled = true;

    @Size(max = 255, message = "Logo file ID must be less than 255 characters")
    private String logoFileId;

    private String gstin;
}