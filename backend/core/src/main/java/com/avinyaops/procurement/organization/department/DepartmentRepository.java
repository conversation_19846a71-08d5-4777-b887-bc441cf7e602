package com.avinyaops.procurement.organization.department;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {
    
    @Query("SELECT d FROM Department d WHERE d.organization.id = :organizationId")
    List<Department> findAllByOrganizationId(@Param("organizationId") Long organizationId);
    
    @Query("SELECT d FROM Department d WHERE d.id = :id")
    @NonNull Optional<Department> findById(@Param("id") @NonNull Long id);

    Optional<Department> findByIdAndOrganizationId(@Param("id") Long id, @Param("organizationId") Long organizationId);


    Optional<Department> findByDepartmentHeadIdAndOrganizationId(@Param("departmentHeadId") Long departmentHeadId, @Param("organizationId") Long organizationId);

    @Query("SELECT d FROM Department d WHERE d.name = :name")
    Optional<Department> findByName(@Param("name") String name);
    
    //TODO: test the sql restriction(on base auditable entity) and optimize the query to check existence by using SELECT EXISTS
    @Query("SELECT CASE WHEN COUNT(d) > 0 THEN true ELSE false END FROM Department d " +
            "WHERE d.name = :name AND d.organization.id = :organizationId")
    boolean existsByNameAndOrganizationId(@Param("name") String name, @Param("organizationId") Long organizationId);
} 