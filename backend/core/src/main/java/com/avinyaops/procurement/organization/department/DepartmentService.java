package com.avinyaops.procurement.organization.department;

import java.util.List;

public interface DepartmentService {
    DepartmentDTO createDepartment(Long organizationId, DepartmentDTO departmentDTO);

    DepartmentDTO updateDepartment(Long organizationId, Long id, DepartmentDTO departmentDTO);

    void deleteDepartment(Long organizationId, Long departmentId);

    DepartmentDTO getDepartment(Long organizationId, Long id);

    List<DepartmentDTO> getAllDepartmentsByOrganization(Long organizationId);
} 