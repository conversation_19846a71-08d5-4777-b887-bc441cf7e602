package com.avinyaops.procurement.product;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface OrganizationProductRepository extends JpaRepository<OrganizationProduct, Long> {
    boolean existsByOrganizationIdAndName(Long organizationId, String name);

    Optional<OrganizationProduct> findByOrganizationIdAndId(Long organizationId, Long id);

    List<OrganizationProduct> findAllByOrganizationId(Long organizationId);

    List<OrganizationProduct> findAllByOrganizationIdAndCategoryId(Long organizationId, Long categoryId);

    List<OrganizationProduct> findAllByOrganizationIdAndSubCategoryId(Long organizationId, Long subCategoryId);

    @Query("SELECT p FROM OrganizationProduct p WHERE p.organizationId = :organizationId AND " +
            "(LOWER(p.name) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
            "LOWER(p.description) LIKE LOWER(CONCAT('%', :query, '%')))")
    List<OrganizationProduct> searchByNameOrDescription(@Param("organizationId") Long organizationId,
            @Param("query") String query);
}
