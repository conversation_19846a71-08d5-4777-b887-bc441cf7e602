package com.avinyaops.procurement.product;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/v1/products")
@RequiredArgsConstructor
@Tag(name = "Product Management", description = "APIs for managing products")
public class ProductController {

    private final ProductService productService;

    @Operation(summary = "Create a new product", description = "Creates a new product in the system. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Product created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "409", description = "Product with same name already exists")
    })
    @PostMapping
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ProductResponseDTO> createProduct(
            @Valid @ModelAttribute ProductRequestDTO productRequestDTO) {
        return ResponseEntity.ok(productService.createProduct(productRequestDTO));
    }

    @Operation(summary = "Update an existing product", description = "Updates an existing product in the system. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Product updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Product not found"),
        @ApiResponse(responseCode = "409", description = "Product with same name already exists")
    })
    @PutMapping("/{id}")
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<ProductResponseDTO> updateProduct(
            @Parameter(description = "Product ID") @PathVariable Long id,
            @Valid @ModelAttribute ProductRequestDTO productRequestDTO) {
        return ResponseEntity.ok(productService.updateProduct(id, productRequestDTO));
    }

    @Operation(summary = "Delete a product", description = "Deletes a product from the system. Requires ADMIN role.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Product deleted successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Product not found")
    })
    @DeleteMapping("/{id}")
    // @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deleteProduct(
            @Parameter(description = "Product ID") @PathVariable Long id) {
        productService.deleteProduct(id);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Get a product by ID", description = "Retrieves a product by its ID. Requires authentication.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Product found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Product not found")
    })
    @GetMapping("/{id}")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ProductResponseDTO> getProduct(
            @Parameter(description = "Product ID") @PathVariable Long id) {
        return ResponseEntity.ok(productService.getProduct(id));
    }

    @Operation(summary = "Get all products", description = "Retrieves all products. If organizationId is provided in the request body, returns products for that organization and global products. Otherwise, returns only global products.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Products retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<ProductResponseDTO>> getAllProducts() {
        return ResponseEntity.ok(productService.getAllProducts());
    }

    //TODO: with reference to the product category controller update this api
    @Operation(summary = "Get products by category", description = "Retrieves all products in a specific category. If organizationId is provided, returns products for that organization and global products. Otherwise, returns only global products.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Products retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Category not found")
    })
    @GetMapping("/category/{categoryId}")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<ProductResponseDTO>> getProductsByCategory(
            @Parameter(description = "Category ID") @PathVariable Long categoryId) {
        return ResponseEntity.ok(productService.getProductsByCategory(categoryId));
    }

    @Operation(summary = "Get products by sub-category", description = "Retrieves all products in a specific sub-category. If organizationId is provided, returns products for that organization and global products. Otherwise, returns only global products.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Products retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "404", description = "Sub-category not found")
    })
    @GetMapping("/subcategory/{subCategoryId}")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<ProductResponseDTO>> getProductsBySubCategory(
            @Parameter(description = "Sub-category ID") @PathVariable Long subCategoryId) {
        return ResponseEntity.ok(productService.getProductsBySubCategory(subCategoryId));
    }

    @Operation(summary = "Search products", description = "Searches products by name or description. If organizationId is provided, searches in that organization and global products. Otherwise, searches only in global products.")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping("/search")
    // @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<ProductResponseDTO>> searchProducts(
            @Parameter(description = "Search query") @RequestParam String query) {
        return ResponseEntity.ok(productService.searchProducts(query));
    }

    @Operation(summary = "Create a new organization product", description = "Creates a new product for an organization. Requires ADMIN role.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organization product created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "403", description = "Access denied"),
            @ApiResponse(responseCode = "409", description = "Product with same name already exists")
    })
    @PostMapping("/org-product")
    public ResponseEntity<OrganizationProductResponseDTO> createOrganizationProduct(
            @RequestParam Long organizationId,
            @Valid @ModelAttribute OrganizationProductRequestDTO organizationProductRequestDTO) {
        return ResponseEntity.ok(productService.createOrganizationProduct(organizationId, organizationProductRequestDTO));
    }

    @Operation(summary = "Update an organization product", description = "Updates an existing organization product. Requires ADMIN role.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organization product updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "403", description = "Access denied"),
            @ApiResponse(responseCode = "404", description = "Organization product not found"),
            @ApiResponse(responseCode = "409", description = "Product with same name already exists")
    })
    @PutMapping("/org-product/{id}")
    public ResponseEntity<OrganizationProductResponseDTO> updateOrganizationProduct(
            @RequestParam Long organizationId,
            @PathVariable Long id,
            @Valid @ModelAttribute OrganizationProductRequestDTO organizationProductRequestDTO) {
        return ResponseEntity.ok(productService.updateOrganizationProduct(organizationId, id, organizationProductRequestDTO));
    }

    @Operation(summary = "Delete an organization product", description = "Deletes an organization product. Requires ADMIN role.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Organization product deleted successfully"),
            @ApiResponse(responseCode = "403", description = "Access denied"),
            @ApiResponse(responseCode = "404", description = "Organization product not found")
    })
    @DeleteMapping("/org-product/{id}")
    public ResponseEntity<Void> deleteOrganizationProduct(
            @RequestParam Long organizationId,
            @PathVariable Long id) {
        productService.deleteOrganizationProduct(organizationId, id);
        return ResponseEntity.noContent().build();
    }

    @Operation(summary = "Get an organization product by ID", description = "Retrieves an organization product by its ID. Requires authentication.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organization product found"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Organization product not found")
    })
    @GetMapping("/org-product/{id}")
    public ResponseEntity<OrganizationProductResponseDTO> getOrganizationProduct(
            @RequestParam Long organizationId,
            @PathVariable Long id) {
        return ResponseEntity.ok(productService.getOrganizationProduct(organizationId, id));
    }

    @Operation(summary = "Get all organization products", description = "Retrieves all products for an organization. Requires authentication.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organization products retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping("/org-products")
    public ResponseEntity<List<OrganizationProductResponseDTO>> getAllOrganizationProducts(
            @RequestParam Long organizationId) {
        return ResponseEntity.ok(productService.getAllOrganizationProducts(organizationId));
    }

    @Operation(summary = "Get organization products by category", description = "Retrieves all organization products in a specific category. Requires authentication.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organization products retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Category not found")
    })
    @GetMapping("/org-products/category/{categoryId}")
    public ResponseEntity<List<OrganizationProductResponseDTO>> getOrganizationProductsByCategory(
            @RequestParam Long organizationId,
            @PathVariable Long categoryId) {
        return ResponseEntity.ok(productService.getOrganizationProductsByCategory(organizationId, categoryId));
    }

    @Operation(summary = "Get organization products by sub-category", description = "Retrieves all organization products in a specific sub-category. Requires authentication.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Organization products retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "404", description = "Sub-category not found")
    })
    @GetMapping("/org-products/subcategory/{subCategoryId}")
    public ResponseEntity<List<OrganizationProductResponseDTO>> getOrganizationProductsBySubCategory(
            @RequestParam Long organizationId,
            @PathVariable Long subCategoryId) {
        return ResponseEntity.ok(productService.getOrganizationProductsBySubCategory(organizationId, subCategoryId));
    }

    @Operation(summary = "Search organization products", description = "Searches organization products by name or description. Requires authentication.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Search completed successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @GetMapping("/org-products/search")
    public ResponseEntity<List<OrganizationProductResponseDTO>> searchOrganizationProducts(
            @RequestParam Long organizationId,
            @RequestParam String query) {
        return ResponseEntity.ok(productService.searchOrganizationProducts(organizationId, query));
    }
}
