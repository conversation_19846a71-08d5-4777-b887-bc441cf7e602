package com.avinyaops.procurement.product;

import java.util.List;

public interface ProductService {
    ProductResponseDTO createProduct(ProductRequestDTO productRequestDTO);
    
    ProductResponseDTO updateProduct(Long id, ProductRequestDTO productDTO);
    
    void deleteProduct(Long id);
    
    ProductResponseDTO getProduct(Long id);
    
    List<ProductResponseDTO> getAllProducts();
    
    List<ProductResponseDTO> getProductsByCategory(Long categoryId);
    
    List<ProductResponseDTO> getProductsBySubCategory(Long subCategoryId);
    
    List<ProductResponseDTO> searchProducts(String query);

    OrganizationProductResponseDTO createOrganizationProduct(Long organizationId, OrganizationProductRequestDTO organizationProductRequestDTO);
    
    OrganizationProductResponseDTO updateOrganizationProduct(Long organizationId, Long id, OrganizationProductRequestDTO organizationProductRequestDTO);
    
    void deleteOrganizationProduct(Long organizationId, Long id);
    
    OrganizationProductResponseDTO getOrganizationProduct(Long organizationId, Long id);
    
    List<OrganizationProductResponseDTO> getAllOrganizationProducts(Long organizationId);
    
    List<OrganizationProductResponseDTO> getOrganizationProductsByCategory(Long organizationId, Long categoryId);
    
    List<OrganizationProductResponseDTO> getOrganizationProductsBySubCategory(Long organizationId, Long subCategoryId);
    
    List<OrganizationProductResponseDTO> searchOrganizationProducts(Long organizationId, String query);
} 