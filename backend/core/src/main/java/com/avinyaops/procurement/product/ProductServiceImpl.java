package com.avinyaops.procurement.product;

import com.avinyaops.fileupload.FileStorageService;
import com.avinyaops.procurement.product.category.ProductCategory;
import com.avinyaops.procurement.product.category.ProductCategoryRepository;
import com.avinyaops.procurement.product.category.ProductSubCategory;
import com.avinyaops.procurement.product.category.ProductSubCategoryRepository;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class ProductServiceImpl implements ProductService {

    private final ProductRepository productRepository;
    private final ProductCategoryRepository categoryRepository;
    private final ProductSubCategoryRepository subCategoryRepository;
    private final OrganizationProductRepository organizationProductRepository;
    private final FileStorageService fileStorageService;
    private final String productImageBaseFolderPath = "product";
    private final String organizationProductBaseFolderPath = "organizationproduct";

    @Override
    public ProductResponseDTO createProduct(ProductRequestDTO productRequestDTO) {
        if (productRepository.existsByName(productRequestDTO.getName())) {
            throw new ResourceAlreadyExistsException(
                    "Product with name " + productRequestDTO.getName() + " already exists");
        }

        // Validate category and sub-category
        ProductCategory category = categoryRepository
                .findById(productRequestDTO.getCategoryId())
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Category not found with id: " + productRequestDTO.getCategoryId()));

        ProductSubCategory subCategory = null;
        if (productRequestDTO.getSubCategoryId() != null) {
            subCategory = subCategoryRepository
                    .findById(productRequestDTO.getSubCategoryId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Sub-category not found with id: "
                                    + productRequestDTO.getSubCategoryId()));

            // Validate sub-category belongs to the category
            if (!subCategory.getCategory().getId().equals(category.getId())) {
                throw new IllegalArgumentException(
                        "Sub-category does not belong to the specified category");
            }
        }

        Product product = Product.builder()
                .name(productRequestDTO.getName())
                .description(productRequestDTO.getDescription())
                .category(category)
                .subCategory(subCategory)
                .imageFileId(Optional.ofNullable(productRequestDTO.getImageFile())
                        .map(imageFile -> fileStorageService
                                .uploadFile(imageFile, productImageBaseFolderPath)
                                .getFileId())
                        .orElse(null))
                .attributes(productRequestDTO.getAttributes())
                .build();

        return mapProductToDTO(productRepository.save(product));
    }

    @Override
    public ProductResponseDTO updateProduct(Long id, ProductRequestDTO productRequestDTO) {
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + id));

        // Check if another product with same name exists for the organization
        if (!product.getName().equals(productRequestDTO.getName()) &&
                productRepository.existsByName(productRequestDTO.getName())) {
            throw new ResourceAlreadyExistsException(
                    "Product with name " + productRequestDTO.getName() + " already exists");
        }

        // Validate category and sub-category
        ProductCategory category = categoryRepository
                .findById(productRequestDTO.getCategoryId())
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Category not found with id: " + productRequestDTO.getCategoryId()));

        ProductSubCategory subCategory = null;
        if (productRequestDTO.getSubCategoryId() != null) {
            subCategory = subCategoryRepository
                    .findById(productRequestDTO.getSubCategoryId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Sub-category not found with id: "
                                    + productRequestDTO.getSubCategoryId()));

            // Validate sub-category belongs to the category
            if (!subCategory.getCategory().getId().equals(category.getId())) {
                throw new IllegalArgumentException(
                        "Sub-category does not belong to the specified category");
            }
        }

        product.setName(productRequestDTO.getName());
        product.setDescription(productRequestDTO.getDescription());
        product.setCategory(category);
        product.setSubCategory(subCategory);
        product.setAttributes(productRequestDTO.getAttributes());

        if (productRequestDTO.getImageFile() != null && !productRequestDTO.getImageFile().isEmpty()) {
            String newImageFileIds = fileStorageService
                    .uploadFile(productRequestDTO.getImageFile(), productImageBaseFolderPath)
                    .getFileId();
            product.setImageFileId(newImageFileIds);
        } else if (productRequestDTO.isDeleteImage() == true && product.getImageFileId() != null
                && !product.getImageFileId().isEmpty()) {
            fileStorageService.deleteFile(product.getImageFileId());
            product.setImageFileId(null);
        }

        return mapProductToDTO(productRepository.save(product));
    }

    @Override
    public void deleteProduct(Long id) {
        Product product = productRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + id));
        if (product.getImageFileId() != null && !product.getImageFileId().isBlank()) {
            fileStorageService.deleteFile(product.getImageFileId());
        }

        product.softDelete();
        productRepository.save(product);
    }

    @Override
    public ProductResponseDTO getProduct(Long id) {
        return productRepository.findById(id)
                .map(this::mapProductToDTO)
                .orElseThrow(() -> new ResourceNotFoundException("Product not found with id: " + id));
    }

    @Override
    public List<ProductResponseDTO> getAllProducts() {
        return productRepository.findAll()
                .stream()
                .map(this::mapProductToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductResponseDTO> getProductsByCategory(Long categoryId) {
        return productRepository.findAllByCategoryId(categoryId)
                .stream()
                .map(this::mapProductToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductResponseDTO> getProductsBySubCategory(Long subCategoryId) {
        return productRepository.findAllBySubCategoryId(subCategoryId)
                .stream()
                .map(this::mapProductToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ProductResponseDTO> searchProducts(String query) {
        return productRepository.searchByNameOrDescription(query)
                .stream()
                .map(this::mapProductToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public OrganizationProductResponseDTO createOrganizationProduct(Long organizationId,
            OrganizationProductRequestDTO organizationProductRequestDTO) {

        if (organizationProductRepository.existsByOrganizationIdAndName(
                organizationId, organizationProductRequestDTO.getName())) {
            throw new ResourceAlreadyExistsException(
                    "Product with name " + organizationProductRequestDTO.getName() + " already exists");
        }

        // Validate category and sub-category
        ProductCategory category = categoryRepository
                .findById(organizationProductRequestDTO.getCategoryId())
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Category not found with id: " + organizationProductRequestDTO.getCategoryId()));

        ProductSubCategory subCategory = null;
        if (organizationProductRequestDTO.getSubCategoryId() != null) {
            subCategory = subCategoryRepository
                    .findById(organizationProductRequestDTO.getSubCategoryId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Sub-category not found with id: "
                                    + organizationProductRequestDTO.getSubCategoryId()));

            // Validate sub-category belongs to the category
            if (!subCategory.getCategory().getId().equals(category.getId())) {
                throw new IllegalArgumentException(
                        "Sub-category does not belong to the specified category");
            }
        }

        OrganizationProduct organizationProduct = OrganizationProduct.builder()
                .name(organizationProductRequestDTO.getName())
                .description(organizationProductRequestDTO.getDescription())
                .category(category)
                .subCategory(subCategory)
                .imageFileId(Optional.ofNullable(organizationProductRequestDTO.getImageFile())
                        .map(imageFile -> fileStorageService
                                .uploadFile(imageFile, organizationProductBaseFolderPath)
                                .getFileId())
                        .orElse(null))
                .organizationId(organizationId)
                .attributes(organizationProductRequestDTO.getAttributes())
                .build();

        return mapOrganizationProductToDTO(organizationProductRepository.save(organizationProduct));
    }

    @Override
    public OrganizationProductResponseDTO updateOrganizationProduct(Long organizationId, Long id,
            OrganizationProductRequestDTO organizationProductRequestDTO) {
        OrganizationProduct organizationProduct = organizationProductRepository.findByOrganizationIdAndId(
                organizationId, id)
                .orElseThrow(() -> new ResourceNotFoundException("Organization product not found with id: " + id));

        // Check for duplicate name within the organization
        if (!organizationProduct.getName().equals(organizationProductRequestDTO.getName()) &&
                organizationProductRepository.existsByOrganizationIdAndName(
                        organizationId, organizationProductRequestDTO.getName())) {
            throw new ResourceAlreadyExistsException(
                    "Product with name " + organizationProductRequestDTO.getName() + " already exists");
        }

        // Validate category and sub-category
        ProductCategory category = categoryRepository
                .findById(organizationProductRequestDTO.getCategoryId())
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Category not found with id: " + organizationProductRequestDTO.getCategoryId()));

        ProductSubCategory subCategory = null;
        if (organizationProductRequestDTO.getSubCategoryId() != null) {
            subCategory = subCategoryRepository
                    .findById(organizationProductRequestDTO.getSubCategoryId())
                    .orElseThrow(() -> new ResourceNotFoundException(
                            "Sub-category not found with id: "
                                    + organizationProductRequestDTO.getSubCategoryId()));

            if (!subCategory.getCategory().getId().equals(category.getId())) {
                throw new IllegalArgumentException(
                        "Sub-category does not belong to the specified category");
            }
        }

        organizationProduct.setName(organizationProductRequestDTO.getName());
        organizationProduct.setDescription(organizationProductRequestDTO.getDescription());
        organizationProduct.setCategory(category);
        organizationProduct.setSubCategory(subCategory);
        organizationProduct.setAttributes(organizationProductRequestDTO.getAttributes());

        if (organizationProductRequestDTO.getImageFile() != null
                && !organizationProductRequestDTO.getImageFile().isEmpty()) {
            String newImageFileId = fileStorageService
                    .uploadFile(organizationProductRequestDTO.getImageFile(), organizationProductBaseFolderPath)
                    .getFileId();
            organizationProduct.setImageFileId(newImageFileId);
        } else if (organizationProductRequestDTO.isDeleteImage() == true && organizationProduct.getImageFileId() != null
                && !organizationProduct.getImageFileId().isEmpty()) {
            fileStorageService.deleteFile(organizationProduct.getImageFileId());
            organizationProduct.setImageFileId(null);
        }

        return mapOrganizationProductToDTO(organizationProductRepository.save(organizationProduct));
    }

    @Override
    public void deleteOrganizationProduct(Long organizationId, Long id) {
        OrganizationProduct organizationProduct = organizationProductRepository
                .findByOrganizationIdAndId(organizationId, id)
                .orElseThrow(() -> new ResourceNotFoundException("Organization product not found with id: " + id));
        if (organizationProduct.getImageFileId() != null && !organizationProduct.getImageFileId().isBlank()) {
            fileStorageService.deleteFile(organizationProduct.getImageFileId());
        }
        organizationProduct.softDelete();
        organizationProductRepository.save(organizationProduct);
    }

    @Override
    public OrganizationProductResponseDTO getOrganizationProduct(Long organizationId, Long id) {
        return organizationProductRepository.findByOrganizationIdAndId(organizationId, id)
                .map(this::mapOrganizationProductToDTO)
                .orElseThrow(() -> new ResourceNotFoundException("Organization product not found with id: " + id));
    }

    @Override
    public List<OrganizationProductResponseDTO> getAllOrganizationProducts(Long organizationId) {
        return organizationProductRepository.findAllByOrganizationId(organizationId)
                .stream()
                .map(this::mapOrganizationProductToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<OrganizationProductResponseDTO> getOrganizationProductsByCategory(Long organizationId,
            Long categoryId) {
        return organizationProductRepository.findAllByOrganizationIdAndCategoryId(organizationId, categoryId)
                .stream()
                .map(this::mapOrganizationProductToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<OrganizationProductResponseDTO> getOrganizationProductsBySubCategory(Long organizationId,
            Long subCategoryId) {
        return organizationProductRepository.findAllByOrganizationIdAndSubCategoryId(organizationId, subCategoryId)
                .stream()
                .map(this::mapOrganizationProductToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<OrganizationProductResponseDTO> searchOrganizationProducts(Long organizationId, String query) {
        return organizationProductRepository.searchByNameOrDescription(organizationId, query)
                .stream()
                .map(this::mapOrganizationProductToDTO)
                .collect(Collectors.toList());
    }

    private ProductResponseDTO mapProductToDTO(Product product) {
        return ProductResponseDTO.builder()
                .id(product.getId())
                .name(product.getName())
                .description(product.getDescription())
                .categoryId(product.getCategory().getId())
                .subCategoryId(product.getSubCategory() != null ? product.getSubCategory().getId()
                        : null)
                .imageUrl(
                        Optional.ofNullable(product.getImageFileId())
                                .filter(fileId -> !fileId.isBlank())
                                .map(fileStorageService::generateViewUrl)
                                .orElse(null))
                .attributes(product.getAttributes())
                .build();
    }

    private OrganizationProductResponseDTO mapOrganizationProductToDTO(OrganizationProduct organizationProduct) {
        return OrganizationProductResponseDTO.builder()
                .id(organizationProduct.getId())
                .organizationId(organizationProduct.getOrganizationId())
                .name(organizationProduct.getName())
                .description(organizationProduct.getDescription())
                .categoryId(organizationProduct.getCategory().getId())
                .subCategoryId(
                        organizationProduct.getSubCategory() != null ? organizationProduct.getSubCategory().getId()
                                : null)
                .imageUrl(
                        Optional.ofNullable(organizationProduct.getImageFileId())
                                .filter(fileId -> !fileId.isBlank())
                                .map(fileStorageService::generateViewUrl)
                                .orElse(null))
                .attributes(organizationProduct.getAttributes())
                .build();
    }
}
