package com.avinyaops.procurement.product.category;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

@Repository
public interface ProductCategoryRepository extends JpaRepository<ProductCategory, Long> {
    @NonNull
    Optional<ProductCategory> findById(@NonNull Long id);

    Optional<ProductCategory> findByName(String name);

    boolean existsByName(String name);
}