package com.avinyaops.procurement.product.category;

import java.util.List;

public interface ProductCategoryService {
    ProductCategoryResponseDTO createCategory(ProductCategoryRequestDTO categoryRequestDTO);
    
    ProductCategoryResponseDTO updateCategory(Long id, ProductCategoryRequestDTO categoryRequestDTO);
    
    void deleteCategory(Long id);
    
    ProductCategoryResponseDTO getCategory(Long id);
    
    List<ProductCategoryResponseDTO> getAllCategories();
    
    ProductSubCategoryResponseDTO createSubCategory(Long categoryId, ProductSubCategoryRequestDTO subCategoryDTO);
    
    ProductSubCategoryResponseDTO updateSubCategory(Long categoryId, Long subCategoryId, ProductSubCategoryRequestDTO subCategoryDTO);
    
    void deleteSubCategory(Long categoryId, Long subCategoryId);
    
    ProductSubCategoryResponseDTO getSubCategory(Long categoryId, Long subCategoryId);
    
    List<ProductSubCategoryResponseDTO> getAllSubCategoriesByCategoryId(Long categoryId);
    
    List<ProductSubCategoryResponseDTO> getAllSubCategories();
} 