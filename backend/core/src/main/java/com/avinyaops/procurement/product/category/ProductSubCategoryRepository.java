package com.avinyaops.procurement.product.category;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

@Repository
public interface ProductSubCategoryRepository extends JpaRepository<ProductSubCategory, Long> {
    @NonNull
    Optional<ProductSubCategory> findById(@NonNull Long id);

    boolean existsByNameAndCategoryId(String name, Long categoryId);

    List<ProductSubCategory> findAllByCategoryId(Long categoryId);
}

