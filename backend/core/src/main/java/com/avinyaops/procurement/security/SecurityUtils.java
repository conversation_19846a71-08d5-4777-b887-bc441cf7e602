package com.avinyaops.procurement.security;

import java.util.Optional;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import com.avinyaops.procurement.user.User;

/**
 * Utility class for retrieving information about the currently authenticated user from the Spring Security context.
 */
public final class SecurityUtils {

    /**
     * Retrieves the ID of the currently authenticated user.
     *
     * @return the user ID if available, or {@code null} if not authenticated
     */
    public static Long getCurrentUserId() {
        return getCurrentUser()
                .map(User::getId)
                .orElse(null);
    }

    /**
     * Retrieves the name of the currently authenticated user.
     *
     * @return the user name if available, or {@code null} if not authenticated
     */
    public static String getCurrentUserName() {
        return getCurrentUser()
                .map(User::getName)
                .orElse(null);
    }

    /**
     * Retrieves the currently authenticated {@link User} from the security context.
     *
     * @return an {@link Optional} containing the current user if authenticated and present, otherwise an empty {@link Optional}
     */
    public static Optional<User> getCurrentUser() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication())
                .filter(Authentication::isAuthenticated)
                .map(Authentication::getPrincipal)
                .filter(User.class::isInstance)
                .map(User.class::cast);
    }
}