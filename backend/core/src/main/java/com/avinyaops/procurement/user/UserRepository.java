package com.avinyaops.procurement.user;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByIdAndOrganizationId(@Param("id") Long id, @Param("organizationId") Long organizationId);

    List<User> findAllByOrganizationId(@Param("organizationId") Long organizationId);

    Optional<User> findByOrganizationIdAndRole(@Param("organizationId") Long organizationId, @Param("role") Role role);

    Optional<User> findByEmail(String email);

    boolean existsByEmail(String email);

    @Query("SELECT u FROM User u WHERE " +
            "(:searchTerm IS NULL OR " +
            "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
            "LOWER(u.name) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
            "(:role IS NULL OR u.role = :role) AND " +
            "(:enabled IS NULL OR u.enabled = :enabled) AND " +
            "u.organization.id = :organizationId")
    List<User> searchUsers(
            @Param("searchTerm") String searchTerm,
            @Param("role") Role role,
            @Param("enabled") Boolean enabled,
            @Param("organizationId") Long organizationId);

    boolean existsByDepartmentId(@Param("departmentId") Long departmentId);

    boolean existsByDesignationId(@Param("designationId") Long designationId);
}