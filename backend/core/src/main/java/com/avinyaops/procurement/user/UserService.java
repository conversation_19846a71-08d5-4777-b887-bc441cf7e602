package com.avinyaops.procurement.user;

import java.util.List;

import com.avinyaops.procurement.organization.Organization;

public interface UserService {
    UserResponse createUser(UserCreateRequest request);

    UserResponse createOrganizationAdmin(OrganizationAdminCreate request, Organization organization);

    UserResponse updateUser(Long id, UserUpdateRequest request);

    void deleteUser(Long id, Long organizationId);

    UserResponse getUser(Long id, Long organizationId);

    UserResponse enableUser(Long id, Long organizationId);

    UserResponse disableUser(Long id, Long organizationId);

    List<UserResponse> getAllUsers(Long organizationId);

    List<UserResponse> searchUsers(String searchTerm, Role role, Boolean enabled, Long organizationId);

    boolean isCurrentUser(Long userId);

    UserResponse getOrganizationAdmin(Long organizationId);

    User getById(Long id);
}