package com.avinyaops.procurement.user;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.avinyaops.procurement.email.EmailRequest;
import com.avinyaops.procurement.email.EmailService;
import com.avinyaops.procurement.organization.Organization;
import com.avinyaops.procurement.organization.OrganizationNotFoundException;
import com.avinyaops.procurement.organization.OrganizationRepository;
import com.avinyaops.procurement.organization.department.Department;
import com.avinyaops.procurement.organization.department.DepartmentRepository;
import com.avinyaops.procurement.organization.designation.Designation;
import com.avinyaops.procurement.organization.designation.DesignationRepository;
import com.avinyaops.procurement.organization.location.LocationRepository;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Transactional
@Slf4j
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    private final OrganizationRepository organizationRepository;
    private final DepartmentRepository departmentRepository;
    private final DesignationRepository designationRepository;
    private final LocationRepository locationRepository;
    private final EmailService emailService;

    @PostConstruct
    private void initializeDefaultAdmin() {
        if (!userRepository.existsByEmail("<EMAIL>")) {
            String password = "admin@123";
            User admin = User.builder()
                    .email("<EMAIL>")
                    .password(passwordEncoder.encode(password))
                    .name("Default Admin User")
                    .phoneNumber("+919876543210")
                    .role(Role.ROLE_SUPER_ADMIN)
                    .enabled(true)
                    .build();
            userRepository.save(admin);
            log.info("Default admin created with email: {}", admin.getEmail());
        }
    }

    @Override
    public UserResponse createUser(UserCreateRequest request) {
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new UserAlreadyExistsException(request.getEmail());
        }

        String password = RandomStringUtils.randomAlphanumeric(12);
        log.info("REMOVE IN PROD PASSWORD ONLY FOR TESTING: " + password);
        User user = User.builder()
                .email(request.getEmail())
                .password(passwordEncoder.encode(password))
                .name(request.getName())
                .phoneNumber(request.getPhoneNumber())
                .organization(organizationRepository.findById(request.getOrganizationId()).orElseThrow(() -> new OrganizationNotFoundException(request.getOrganizationId())))
                .department(departmentRepository.findByIdAndOrganizationId(request.getDepartmentId(), request.getOrganizationId()).orElseThrow())
                .designation(designationRepository.findByIdAndOrganizationId(request.getDesignationId(), request.getOrganizationId()).orElseThrow())
                .role(Role.ROLE_USER)
                .location(locationRepository.findByIdAndOrganizationId(request.getLocationId(), request.getOrganizationId()).orElseThrow())
                .enabled(true)
                .reportingManager(request.getReportingManagerId() != null ? userRepository.findById(request.getReportingManagerId()).orElseThrow() : null)
                .build();

        user = userRepository.save(user);

        emailService.sendEmailAsync(EmailRequest.builder()
                .to(user.getEmail())
                .subject("Welcome to AvinyaOps")
                .templateName("welcome")
                .templateData(Map.of(
                        "name", user.getName(),
                        "email", user.getEmail()
                        ,"password", password
                        ))
                .isHtml(true)
                .build());
        return mapToResponse(user);
    }

    @Override
    public UserResponse createOrganizationAdmin(OrganizationAdminCreate request, Organization organization) {
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new UserAlreadyExistsException(request.getEmail());
        }

        String password = RandomStringUtils.randomAlphanumeric(12);


        User user = User.builder()
                .email(request.getEmail())
                .password(passwordEncoder.encode(password))
                .name(request.getName())
                .phoneNumber(request.getPhoneNumber())
                .organization(organization)
                .role(Role.ROLE_ORG_ADMIN)
                .enabled(true)
                .build();

        user = userRepository.save(user);

        emailService.sendEmailAsync(EmailRequest.builder()
                .to(user.getEmail())
                .subject("Welcome to AvinyaOps")
                .templateName("welcome")
                .templateData(Map.of(
                        "name", user.getName(),
                        "email", user.getEmail(),
                        "password", password
                ))
                .isHtml(true)
                .build());
        return mapToResponse(user);
    }

    @Override
    public UserResponse updateUser(Long id, UserUpdateRequest request) {
        User user = userRepository.findByIdAndOrganizationId(id, request.getOrganizationId())
                .orElseThrow(() -> new UserNotFoundException(id));

        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(request.getEmail())) {
                throw new UserAlreadyExistsException(request.getEmail());
            }
            user.setEmail(request.getEmail());
        }

        if (request.getPassword() != null) {
            user.setPassword(passwordEncoder.encode(request.getPassword()));
        }

        if (request.getName() != null) {
            user.setName(request.getName());
        }

        if (request.getRole() != null) {
            user.setRole(request.getRole());
        }

        if(request.getPhoneNumber() != null) {
            user.setPhoneNumber(request.getPhoneNumber());
        }

        if(request.getDepartmentId() != null) {
            user.setDepartment(departmentRepository.findById(request.getDepartmentId()).orElseThrow());
        }
        if(request.getDesignationId() != null) {
            user.setDesignation(designationRepository.findById(request.getDesignationId()).orElseThrow());
        }

        if(request.getLocationId() != null) {
            user.setLocation(locationRepository.findById(request.getLocationId()).orElseThrow());
        }

        if (request.getEnabled() != null) {
            user.setEnabled(request.getEnabled());
        }
        
        if((request.getReportingManagerId() != null) && (request.getReportingManagerId() != user.getId())) {
            user.setReportingManager(userRepository.findById(request.getReportingManagerId()).orElseThrow());
        }

        user = userRepository.save(user);
        return mapToResponse(user);
    }

    @Override
    public void deleteUser(Long id, Long organizationId) {
        User user = userRepository.findByIdAndOrganizationId(id, organizationId)
                .orElseThrow(() -> new UserNotFoundException(id));

        if(isCurrentUser(id)){
            throw new IllegalArgumentException("Cannot delete self");
        }
        user.softDelete();
        userRepository.save(user);
    }

    @Override
    @Transactional(readOnly = true)
    public UserResponse getUser(Long id, Long organizationId) {
        User user = userRepository.findByIdAndOrganizationId(id, organizationId)
                .orElseThrow(() -> new UserNotFoundException(id));
        return mapToResponse(user);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse> getAllUsers(Long organizationId) {
        return userRepository.findAllByOrganizationId(organizationId)
                .stream().map(this::mapToResponse).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public UserResponse getOrganizationAdmin(Long organizationId) {
        return userRepository.findByOrganizationIdAndRole(organizationId, Role.ROLE_ORG_ADMIN)
                .map(this::mapToResponse)
                .orElseThrow(() -> new UserNotFoundException("Organization admin not found for organization id: " + organizationId));
    }

    @Override
    public UserResponse enableUser(Long id, Long organizationId) {
        User user = userRepository.findByIdAndOrganizationId(id, organizationId)
                .orElseThrow(() -> new UserNotFoundException(id));
        user.setEnabled(true);
        user = userRepository.save(user);
        return mapToResponse(user);
    }

    @Override
    public UserResponse disableUser(Long id, Long organizationId) {
        User user = userRepository.findByIdAndOrganizationId(id, organizationId)
                .orElseThrow(() -> new UserNotFoundException(id));
        log.info("User: {}", id);
        if (isCurrentUser(id)) {
            throw new IllegalArgumentException("Cannot disable self");
        }
        user.setEnabled(false);
        user = userRepository.save(user);
        return mapToResponse(user);
    }

    @Override
    @Transactional(readOnly = true)
    public List<UserResponse> searchUsers(String searchTerm, Role role, Boolean enabled, Long organizationId) {
        return userRepository.searchUsers(searchTerm, role, enabled, organizationId)
                .stream().map(this::mapToResponse).collect(Collectors.toList());
    }

    @Override
    public boolean isCurrentUser(Long userId) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.info("Authentication: {}", authentication);
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }
        return userRepository.findById(userId)
                .map(user -> user.getEmail().equals(authentication.getName()))
                .orElse(false);
    }

    @Override 
    public User getById(Long id) {
        return userRepository.findById(id).orElseThrow(() -> new UserNotFoundException(id));
    }

    private UserResponse mapToResponse(User user) {
        return UserResponse.builder()
                .id(user.getId())
                .email(user.getEmail())
                .name(user.getName())
                .role(user.getRole())
                .enabled(user.isEnabled())
                .phoneNumber(user.getPhoneNumber())
                .reportingManagerId(Optional.ofNullable(user.getReportingManager()).map(User::getId).orElse(null))
                .organizationId(Optional.ofNullable(user.getOrganization()).map(Organization::getId).orElse(null))
                .departmentId(Optional.ofNullable(user.getDepartment()).map(Department::getId).orElse(null))
                .designationId(Optional.ofNullable(user.getDesignation()).map(Designation::getId).orElse(null))
                .locationId(Optional.ofNullable(user.getLocation()).map(location -> location.getId()).orElse(null))
                .build();
    }
}