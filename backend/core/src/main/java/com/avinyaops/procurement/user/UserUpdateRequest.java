package com.avinyaops.procurement.user;

import com.avinyaops.procurement.util.phoneNumber.ValidPhoneNumber;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserUpdateRequest {
    @Email(message = "Invalid email format")
    @Size(max = 100, message = "Email must be less than 100 characters")
    private String email;

    @Size(min = 8, max = 100, message = "Password must be between 8 and 100 characters")
    @Pattern(regexp = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,}$",
            message = "Password must contain at least one digit, one uppercase letter, one lowercase letter, and one special character")
    private String password;

    @Size(max = 50, message = "Name must be less than 50 characters")
    private String name;

    private Role role;

    @Size(max = 20, message = "Phone number must be less than 20 characters")
    @ValidPhoneNumber
    private String phoneNumber;

    @NotNull
    private Long organizationId;

    private Long departmentId;

    private Long designationId;

    private Boolean enabled;

    private Long reportingManagerId;

    private Long locationId;
} 