package com.avinyaops.procurement.workflow;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApproverTimelineResponse {
    private Long id;
    private Integer executionOrder;
    private Long approverId;
    private String approverName; // User or role name
    private ApprovalStatus status;
    private String comment;
    private LocalDateTime actionDate;
    private Boolean isCurrent;
}