package com.avinyaops.procurement.workflow;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

public interface OrganizationFormsRepository extends JpaRepository<OrganizationForms, Long> {

    List<OrganizationForms> findAllByOrganizationId(Long organizationId);
    
    Optional<OrganizationForms> findByOrganizationIdAndId(Long organizationId, Long id);

    Optional<OrganizationForms> findByOrganizationIdAndName(Long organizationId, String name);
}