package com.avinyaops.procurement.workflow;

import java.time.Instant;

import com.avinyaops.procurement.audit.BaseAuditableEntity;
import com.avinyaops.procurement.idgenerator.AvinyaId;
import com.avinyaops.procurement.organization.Organization;
import com.avinyaops.procurement.user.User;

import jakarta.persistence.*;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "record_approvals")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
@Builder
public class RecordApproval extends BaseAuditableEntity {
    @Id
    @AvinyaId
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_id", nullable = false)
    private Organization organization;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_form_id", nullable = false)
    private OrganizationForms organizationForms;

    @NotNull
    @Column(nullable = false)
    private Long recordId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workflow_initiator_user_id", nullable = false)
    private User workflowInitiatorUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approval_pending_with_user_id")
    private User approvalPendingWithUser;

    @Column(nullable = false)
    private Instant approvalInitiatedDate;

    @Column
    private Instant lastActionDate;

    @Column
    private String comment;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    @Builder.Default
    private ApprovalStatus approvalStatus = ApprovalStatus.PENDING;

    // @AssertTrue(message = "Approval pending user must be set when status is not APPROVED")
    // private boolean isApprovalPendingUserValid() {
    //     if (approvalStatus == ApprovalStatus.APPROVED) {
    //         return true; // Can be null when approved
    //     }
    //     return approvalPendingWithUser != null; // Must not be null for other statuses
    // }
}
