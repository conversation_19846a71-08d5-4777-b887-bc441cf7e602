package com.avinyaops.procurement.workflow;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.AllArgsConstructor;

@RestController
@RequestMapping("/api/v1/organizations/{organizationId}/workflows")
@AllArgsConstructor
public class WorkflowController {
    
    private final WorkflowDefinitionService workflowDefinitionService;
    private final WorkflowService workflowService;
    
    // Workflow Definition endpoints
    
    @GetMapping("/definitions")
    public ResponseEntity<List<WorkflowDefinitionResponse>> getAllWorkflowDefinitions(
            @PathVariable Long organizationId) {
        List<WorkflowDefinition> definitions = workflowDefinitionService.getAllByOrganizationId(organizationId);
        List<WorkflowDefinitionResponse> responses = definitions.stream()
                .map(this::toWorkflowDefinitionResponse)
                .toList();
        
        return ResponseEntity.ok(responses);
    }
    
    @GetMapping("/definitions/{definitionId}")
    public ResponseEntity<WorkflowDefinitionResponse> getWorkflowDefinition(
            @PathVariable Long organizationId,
            @PathVariable Long definitionId) {
        WorkflowDefinition definition = workflowDefinitionService.get(organizationId, definitionId);
        return ResponseEntity.ok(toWorkflowDefinitionResponse(definition));
    }
    
    @PostMapping("/definitions")
    public ResponseEntity<WorkflowDefinitionResponse> createWorkflowDefinition(
            @PathVariable Long organizationId,
            @RequestBody WorkflowDefinitionRequest request) {
        // Convert request to entity
        WorkflowDefinition definition = toWorkflowDefinitionEntity(request);
        definition.setOrganizationId(organizationId);

        // Save the definition
        WorkflowDefinition saved = workflowDefinitionService.save(definition);

        return ResponseEntity.ok(toWorkflowDefinitionResponse(saved));
    }

    @PutMapping("/definitions/{definitionId}")
    public ResponseEntity<WorkflowDefinitionResponse> updateWorkflowDefinition(
            @PathVariable Long organizationId,
            @PathVariable Long definitionId,
            @RequestBody WorkflowDefinitionRequest request) {
        // Get existing definition
        WorkflowDefinition existing = workflowDefinitionService.get(organizationId, definitionId);
        if (existing == null) {
            return ResponseEntity.notFound().build();
        }

        // Update fields
        existing.setName(request.getName());
        existing.setDescription(request.getDescription());
        existing.setFormId(request.getFormId());
        existing.setAutoApproval(request.getAutoApproval() != null ? request.getAutoApproval() : null);

        // Update criteria
        if (request.getCriteria() != null) {
            List<WorkflowCriteria> criteria = request.getCriteria().stream()
                    .map(this::toWorkflowCriteriaEntity)
                    .collect(Collectors.toList());

            CriteriaConfig criteriaConfig = new CriteriaConfig();
            criteriaConfig.setWorkflowCriteriaList(criteria);
            existing.setCriteriaConfig(criteriaConfig);
        }

        // Update approvers
        if (request.getApprovers() != null) {
            List<Approval> approvers = request.getApprovers().stream()
                    .map(this::toApprovalsEntity)
                    .collect(Collectors.toList());
            existing.setApprovals(approvers);
        }

        // Save the updated definition
        WorkflowDefinition saved = workflowDefinitionService.save(existing);

        return ResponseEntity.ok(toWorkflowDefinitionResponse(saved));
    }

    @DeleteMapping("/definitions/{definitionId}")
    public ResponseEntity<Void> deleteWorkflowDefinition(
            @PathVariable Long organizationId,
            @PathVariable Long definitionId) {
        WorkflowDefinition definition = workflowDefinitionService.get(organizationId, definitionId);
        if (definition == null) {
            return ResponseEntity.notFound().build();
        }

        workflowDefinitionService.delete(organizationId, definitionId);
        return ResponseEntity.noContent().build();
    }

    // Organization Forms endpoints for form types
    @GetMapping("/forms")
    public ResponseEntity<List<OrganizationFormsResponse>> getAllOrganizationForms(
            @PathVariable Long organizationId) {
        List<OrganizationForms> forms = workflowDefinitionService.getAllFormsByOrganizationId(organizationId);
        List<OrganizationFormsResponse> responses = forms.stream()
                .map(this::toOrganizationFormsResponse)
                .toList();

        return ResponseEntity.ok(responses);
    }

    // Workflow Approval endpoints
    
    @PostMapping("/approvals/initiate")
    public ResponseEntity<RecordApprovalResponse> initiateWorkflow(
            @PathVariable Long organizationId,
            @RequestBody InitiateWorkflowRequest request) {

        return ResponseEntity.ok(workflowService.initiateWorkflow(
                organizationId,
                request.getFormId(),
                request.getRecordId(),
                request.getInitiatorUserId()));
    }

    @PutMapping("/approvals/{recordApprovalId}/approve")
    public ResponseEntity<RecordApprovalResponse> approveRecord(
            @PathVariable Long organizationId,
            @PathVariable Long recordApprovalId,
            @RequestBody ApprovalActionRequest request) {
        
        return ResponseEntity.ok(workflowService.approveRecord(
                organizationId,
                recordApprovalId, 
                request.getUserId(), 
                request.getComment()));
    }
    
    @PutMapping("/approvals/{recordApprovalId}/reject")
    public ResponseEntity<RecordApprovalResponse> rejectRecord(
            @PathVariable Long organizationId,
            @PathVariable Long recordApprovalId,
            @RequestBody ApprovalActionRequest request) {

        return ResponseEntity.ok(workflowService.rejectRecord(
                organizationId,
                recordApprovalId,
                request.getUserId(),
                request.getComment()));
    }
    
    @GetMapping("/approvals/pending/user/{userId}")
    public ResponseEntity<List<RecordApprovalResponse>> getPendingApprovals(
            @PathVariable Long organizationId,
            @PathVariable Long userId) {

        return ResponseEntity.ok(workflowService.getPendingApprovals(organizationId, userId));
    }
    
    @GetMapping("/approvals/initiated/user/{userId}")
    public ResponseEntity<List<RecordApprovalResponse>> getInitiatedApprovals(
            @PathVariable Long organizationId,
            @PathVariable Long userId) {
        return ResponseEntity.ok(workflowService.getInitiatedApprovals(organizationId, userId));
    }
    
    @GetMapping("/approvals/{recordApprovalId}/timeline")
    public ResponseEntity<List<ApproverTimelineResponse>> getApprovalTimeline(
            @PathVariable Long organizationId,
            @PathVariable Long recordApprovalId) {
        return ResponseEntity.ok(workflowService.getApprovalTimelineById(organizationId, recordApprovalId));
    }

    @GetMapping("/approvals/{recordId}/timeline")
    public ResponseEntity<List<ApproverTimelineResponse>> getApprovalTimelineByRecordId(
            @PathVariable Long organizationId,
            @PathVariable Long recordId) {
        return ResponseEntity.ok(workflowService.getApprovalTimelineById(organizationId, recordId));
    }
    
    // Helper methods for entity-DTO conversion
    
    private WorkflowDefinitionResponse toWorkflowDefinitionResponse(WorkflowDefinition definition) {
        if (definition == null) {
            return null;
        }
        
        List<WorkflowCriteriaDto> criteriaDtos = null;
        if (definition.getCriteriaConfig() != null && definition.getCriteriaConfig().getWorkflowCriteriaList() != null) {
            criteriaDtos = definition.getCriteriaConfig().getWorkflowCriteriaList().stream()
                    .map(this::toWorkflowCriteriaDto)
                    .collect(Collectors.toList());
        }
        
        List<ApproverConfigDto> approverDtos = null;

        if (definition.getApprovals() != null) {
            approverDtos = definition.getApprovals().stream()
                    .map(this::toApproverConfigDto)
                    .collect(Collectors.toList());
        }
        
        return WorkflowDefinitionResponse.builder()
                .id(definition.getId())
                .organizationId(definition.getOrganizationId())
                .formId(definition.getFormId())
                .name(definition.getName())
                .description(definition.getDescription())
                .isActive(true) // Assuming default is active
                .criteria(criteriaDtos)
                .approvers(approverDtos)
                .autoApproval(definition.getAutoApproval())
                .createdAt(definition.getCreatedDate() != null ? 
                        LocalDateTime.ofInstant(definition.getCreatedDate(), ZoneId.systemDefault()) : null)
                .updatedAt(definition.getLastModifiedDate() != null ? 
                        LocalDateTime.ofInstant(definition.getLastModifiedDate(), ZoneId.systemDefault()) : null)
                .build();
    }
    
    private WorkflowDefinition toWorkflowDefinitionEntity(WorkflowDefinitionRequest request) {
        if (request == null) {
            return null;
        }
        
        List<WorkflowCriteria> criteria = null;
        if (request.getCriteria() != null) {
            criteria = request.getCriteria().stream()
                    .map(this::toWorkflowCriteriaEntity)
                    .collect(Collectors.toList());
        }
        
        List<Approval> approvers = null;
        if (request.getApprovers() != null) {
            approvers = request.getApprovers().stream()
                    .map(this::toApprovalsEntity)
                    .collect(Collectors.toList());
        }
        
        WorkflowDefinition definition = new WorkflowDefinition();
        definition.setName(request.getName());
        definition.setDescription(request.getDescription());
        definition.setFormId(request.getFormId());
        
        // Set criteria
        if (criteria != null) {
            CriteriaConfig criteriaConfig = new CriteriaConfig();
            criteriaConfig.setWorkflowCriteriaList(criteria);
            definition.setCriteriaConfig(criteriaConfig);
        }

        definition.setAutoApproval(request.getAutoApproval() != null ? request.getAutoApproval() : null);
        
        // Set approvers
        definition.setApprovals(approvers != null ? approvers : new ArrayList<>());
        
        return definition;
    }
    
    private WorkflowCriteriaDto toWorkflowCriteriaDto(WorkflowCriteria criteria) {
        if (criteria == null) {
            return null;
        }
        
        return WorkflowCriteriaDto.builder()
                .conjunctiveCondition(criteria.getConjunctiveCondition())
                .fieldName(criteria.getField())
                .operation(criteria.getOperation())
                .values(criteria.getRuleValues())
                .build();
    }
    
    private WorkflowCriteria toWorkflowCriteriaEntity(WorkflowCriteriaDto dto) {
        if (dto == null) {
            return null;
        }
        
        WorkflowCriteria criteria = new WorkflowCriteria();
        criteria.setConjunctiveCondition(dto.getConjunctiveCondition());
        criteria.setField(dto.getFieldName());
        criteria.setOperation(dto.getOperation());
        criteria.setRuleValues(dto.getValues());
        
        return criteria;
    }
    
    private ApproverConfigDto toApproverConfigDto(Approval approvals) {
        if (approvals == null) {
            return null;
        }

        return ApproverConfigDto.builder()
                .id(null) // Approvals doesn't have an ID field
                .executionOrder(0) // Approvals doesn't have an executionOrder field
                .approverType(approvals.getApproverType())
                .approverId(approvals.getApproverId())
                .timeoutDays(null) // Approvals doesn't have a timeoutDays field
                .build();
    }
    
    private Approval toApprovalsEntity(ApproverConfigDto dto) {
        if (dto == null) {
            return null;
        }
        
        Approval approvals = new Approval();
        approvals.setApproverType(dto.getApproverType());
        approvals.setApproverId(dto.getApproverId());
        
        return approvals;
    }
    

    private OrganizationFormsResponse toOrganizationFormsResponse(OrganizationForms form) {
        if (form == null) {
            return null;
        }

        return OrganizationFormsResponse.builder()
                .id(form.getId())
                .organizationId(form.getOrganization() != null ? form.getOrganization().getId() : null)
                .name(form.getName())
                .description(form.getDescription())
                .formSchema(form.getFormSchema())
                .active(form.isActive())
                .createdAt(form.getCreatedDate() != null ?
                        LocalDateTime.ofInstant(form.getCreatedDate(), ZoneId.systemDefault()) : null)
                .updatedAt(form.getLastModifiedDate() != null ?
                        LocalDateTime.ofInstant(form.getLastModifiedDate(), ZoneId.systemDefault()) : null)
                .build();
    }
}