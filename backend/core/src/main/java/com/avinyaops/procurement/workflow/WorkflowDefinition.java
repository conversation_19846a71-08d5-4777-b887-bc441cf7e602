package com.avinyaops.procurement.workflow;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import com.avinyaops.procurement.audit.BaseAuditableEntity;
import com.avinyaops.procurement.idgenerator.AvinyaId;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "workflow_definitions")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@ToString
public class WorkflowDefinition extends BaseAuditableEntity {
    @Id
    @AvinyaId
    private Long id;

    @NotNull
    @JoinColumn(name = "organization_id")
    private Long organizationId;

    @NotNull
    @JoinColumn(name = "form_id")
    private Long formId;

    @Column(nullable = false)
    private String name;

    @Column
    private String description;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "json")
    private CriteriaConfig criteriaConfig = new CriteriaConfig();

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "json")
    private List<Approval> approvals = new ArrayList<>();
    
    
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(columnDefinition = "json")
    private Object followUp;

    @Enumerated(EnumType.STRING)
    private AutoApproval autoApproval;
}