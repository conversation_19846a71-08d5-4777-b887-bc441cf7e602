package com.avinyaops.procurement.workflow;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowDefinitionRequest {
    private Long id;
    private Long formId;
    private String name;
    private String description;
    private Boolean isActive;
    private List<WorkflowCriteriaDto> criteria;
    private List<ApproverConfigDto> approvers;
    private AutoApproval autoApproval;
}