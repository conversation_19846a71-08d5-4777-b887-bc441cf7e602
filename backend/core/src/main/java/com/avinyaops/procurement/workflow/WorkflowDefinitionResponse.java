package com.avinyaops.procurement.workflow;

import java.time.LocalDateTime;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowDefinitionResponse {
    private Long id;
    private Long organizationId;
    private Long formId;
    private String name;
    private String description;
    private Boolean isActive;
    private List<WorkflowCriteriaDto> criteria;
    private List<ApproverConfigDto> approvers;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private AutoApproval autoApproval;
}