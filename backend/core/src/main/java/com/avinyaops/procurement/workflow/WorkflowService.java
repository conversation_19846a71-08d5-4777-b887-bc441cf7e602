package com.avinyaops.procurement.workflow;

import java.util.List;

public interface WorkflowService {
    
    /**
     * Initiates a workflow approval process for a record
     * 
     * @param organizationId the organization ID
     * @param formId the form ID
     * @param recordId the record ID
     * @param initiatorUserId the user ID who initiated the workflow
     * @return the created RecordApproval
     */
    RecordApprovalResponse initiateWorkflow(Long organizationId, Long formId, Long recordId, Long initiatorUserId);
    
    /**
     * Approves a record in the workflow
     * 
     * @param recordApprovalId the record approval ID
     * @param userId the user ID who is approving
     * @param comment optional comment for the approval
     * @return the updated RecordApproval
     */
    RecordApprovalResponse approveRecord(Long organizationId, Long recordApprovalId, Long userId, String comment);
    
    /**
     * Rejects a record in the workflow
     * 
     * @param recordApprovalId the record approval ID
     * @param userId the user ID who is rejecting
     * @param comment optional comment for the rejection
     * @return the updated RecordApproval
     */
    RecordApprovalResponse rejectRecord(Long organizationId, Long recordApprovalId, Long userId, String comment);
    
    /**
     * Gets all pending approvals for a user
     * 
     * @param organizationId the organization ID
     * @param userId the user ID
     * @return list of pending record approvals
     */
    List<RecordApprovalResponse> getPendingApprovals(Long organizationId, Long userId);
    
    /**
     * Gets all approvals initiated by a user
     * 
     * @param organizationId the organization ID
     * @param userId the user ID
     * @return list of record approvals initiated by the user
     */
    List<RecordApprovalResponse> getInitiatedApprovals(Long organizationId, Long userId);
    
    /**
     * Gets the approval timeline for a record
     * 
     * @param recordApprovalId the record approval ID
     * @param organizationId the organization ID
     * @return list of approver timelines
     */
    List<ApproverTimelineResponse> getApprovalTimelineById(Long organizationId, Long recordApprovalId);

    /**
     * Gets the approval timeline for a record
     * 
     * @param recordId the record ID(eg. purchase request ID)
     * @param organizationId the organization ID
     * @return list of approver timelines
     */
    List<ApproverTimelineResponse> getApprovalTimelineByRecordId(Long organizationId, Long recordId);
}