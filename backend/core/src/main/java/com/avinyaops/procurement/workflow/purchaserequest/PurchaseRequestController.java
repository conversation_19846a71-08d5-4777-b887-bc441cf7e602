package com.avinyaops.procurement.workflow.purchaserequest;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/v1/organization/{organizationId}/purchaserequests")
@RequiredArgsConstructor
@Validated
public class PurchaseRequestController {

    private final PurchaseRequestService purchaseRequestService;

    @PostMapping
    public ResponseEntity<PurchaseRequestDTO> createPurchaseRequest(
            @PathVariable Long organizationId,
            @Valid @RequestBody PurchaseRequestDTO purchaseRequestDTO) {
        PurchaseRequestDTO created = purchaseRequestService.createPurchaseRequest(organizationId, purchaseRequestDTO);
        return ResponseEntity.ok(created);
    }

    @GetMapping("/{id}")
    public ResponseEntity<PurchaseRequestDTO> getPurchaseRequestById(
            @PathVariable Long organizationId,
            @PathVariable Long id) {
        PurchaseRequestDTO dto = purchaseRequestService.getPurchaseRequestById(organizationId, id);
        return ResponseEntity.ok(dto);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePurchaseRequest(
            @PathVariable Long organizationId,
            @PathVariable Long id) {
        purchaseRequestService.deletePurchaseRequest(organizationId, id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/requested-by/{userId}")
    public ResponseEntity<List<PurchaseRequestDTO>> getPurchaseRequestsByRequestedById(
            @PathVariable Long organizationId,
            @PathVariable Long userId) {
        List<PurchaseRequestDTO> list = purchaseRequestService.getPurchaseRequestsByRequestedById(organizationId, userId);
        return ResponseEntity.ok(list);
    }

    @GetMapping
    public ResponseEntity<List<PurchaseRequestDTO>> getPurchaseRequestsByOrganizationId(
            @PathVariable Long organizationId) {
        List<PurchaseRequestDTO> list = purchaseRequestService.getPurchaseRequestsByOrganizationId(organizationId);
        return ResponseEntity.ok(list);
    }

    /**
     * Approves a purchase request directly without executing the workflow.
     * This endpoint should be used in exceptional cases only.
     */
    @PutMapping("/{id}/approve")
    public ResponseEntity<Void> approvePurchaseRequest(
            @PathVariable Long organizationId,
            @PathVariable Long id) {
        purchaseRequestService.approvePurchaseRequest(organizationId, id);
        return ResponseEntity.noContent().build();
    }

    /**
     * Rejects a purchase request directly without executing the workflow.
     * This endpoint should be used in exceptional cases only.
     */
    @PutMapping("/{id}/reject")
    public ResponseEntity<Void> rejectPurchaseRequest(
            @PathVariable Long organizationId,
            @PathVariable Long id) {
        purchaseRequestService.rejectPurchaseRequest(organizationId, id);
        return ResponseEntity.noContent().build();
    }
}
