package com.avinyaops.procurement.workflow.purchaserequest;

import java.time.Instant;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseRequestDTO {
    private Long id;

    private Long requestedById;

    private Long organizationId;

    private RequestStatus requestStatus;

    private RequestType requestType;

    private ProcurementSource procurementSource;

    private String description;

    private Instant requestDate;

    private Instant expectedDeliveryDate;

    private Long productCategoryId;

    private List<PurchaseRequestItemDTO> purchaseRequestItems;
}
