package com.avinyaops.procurement.workflow.purchaserequest;

import org.hibernate.annotations.SQLRestriction;

import com.avinyaops.procurement.audit.BaseAuditableEntity;
import com.avinyaops.procurement.idgenerator.AvinyaId;
import com.avinyaops.procurement.product.OrganizationProduct;
import com.avinyaops.procurement.product.Product;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "purchase_request_items")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@SQLRestriction("deleted_date IS NULL")
public class PurchaseRequestItem extends BaseAuditableEntity {
	private static final long serialVersionUID = 1L;
    
    @Id
    @AvinyaId
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "purchase_request_id", nullable = false)
    private PurchaseRequest purchaseRequest;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id")
    private Product product;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "organization_product_id")
    private OrganizationProduct organizationProduct;

    @Column(nullable = false)
    @Min(value = 1, message = "Quantity must be at least 1")
    private int quantity;

    @Column
    private String onlineUrl;

    @PrePersist
    @PreUpdate
    private void validateProductFields() {
        if (product == null && organizationProduct == null) {
            throw new IllegalStateException("Either product or organizationProduct must be set");
        }
        if (product != null && organizationProduct != null) {
            throw new IllegalStateException("Only one of product or organizationProduct should be set, not both");
        }
    }

    public boolean hasProduct() {
        return product != null;
    }

    public boolean hasOrganizationProduct() {
        return organizationProduct != null;
    }

    public Object getProductReference() {
        return hasProduct() ? product : organizationProduct;
    }
}
