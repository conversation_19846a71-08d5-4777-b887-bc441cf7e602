package com.avinyaops.procurement.workflow.purchaserequest;

import jakarta.validation.constraints.AssertTrue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseRequestItemDTO {
    private Long id;

    private Long purchaseRequestId;

    private Long productId;

    private Long organizationProductId;

    private int quantity;

    private String onlineUrl;

    @AssertTrue(message = "Either productId or organizationProductId must be provided, but not both")
    private boolean isEitherProductOrOrganizationProductProvided() {
        return hasProductId() ^ hasOrganizationProductId(); // XOR - exactly one should be true
    }

    public boolean hasProductId() {
        return productId != null;
    }

    public boolean hasOrganizationProductId() {
        return organizationProductId != null;
    }
}
