package com.avinyaops.procurement.workflow.purchaserequest;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;

public interface PurchaseRequestRepository extends JpaRepository<PurchaseRequest, Long>{

    Optional<PurchaseRequest> findByIdAndOrganizationId(Long id, Long organizationId);

    List<PurchaseRequest> findAllByRequestedByIdAndOrganizationId(Long requestedById, Long organizationId);

    List<PurchaseRequest> findAllByOrganizationId(Long organizationId);
}
