package com.avinyaops.procurement.workflow.purchaserequest;

import java.util.List;

public interface PurchaseRequestService {
    public PurchaseRequestDTO createPurchaseRequest(Long organizationId, PurchaseRequestDTO purchaseRequest);

    public PurchaseRequestDTO getPurchaseRequestById(Long organizationId, Long id);

    public void deletePurchaseRequest(Long organizationId, Long id);

    public List<PurchaseRequestDTO> getPurchaseRequestsByRequestedById(Long organizationId, Long userId);

    public List<PurchaseRequestDTO> getPurchaseRequestsByOrganizationId(Long organizationId);

    public void approvePurchaseRequest(Long organizationId, Long id);

    public void rejectPurchaseRequest(Long organizationId, Long id);
}
