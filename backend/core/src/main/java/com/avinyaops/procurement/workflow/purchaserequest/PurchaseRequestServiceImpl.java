package com.avinyaops.procurement.workflow.purchaserequest;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import com.avinyaops.procurement.organization.Organization;
import com.avinyaops.procurement.organization.OrganizationRepository;
import com.avinyaops.procurement.product.OrganizationProduct;
import com.avinyaops.procurement.product.OrganizationProductRepository;
import com.avinyaops.procurement.product.Product;
import com.avinyaops.procurement.product.ProductRepository;
import com.avinyaops.procurement.product.ResourceNotFoundException;
import com.avinyaops.procurement.product.category.ProductCategory;
import com.avinyaops.procurement.product.category.ProductCategoryRepository;
import com.avinyaops.procurement.user.User;
import com.avinyaops.procurement.user.UserRepository;
import com.avinyaops.procurement.workflow.OrganizationForms;
import com.avinyaops.procurement.workflow.OrganizationFormsRepository;
import com.avinyaops.procurement.workflow.WorkflowService;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
@Transactional
public class PurchaseRequestServiceImpl implements PurchaseRequestService {
    private final PurchaseRequestRepository purchaseRequestRepository;
    private final PurchaseRequestItemRepository purchaseRequestItemRepository;
    private final UserRepository userRepository;
    private final OrganizationRepository organizationRepository;
    private final OrganizationFormsRepository organizationFormsRepository;
    private final ProductCategoryRepository productCategoryRepository;
    private final WorkflowService workflowService;
    private final ProductRepository productRepository;
    private final OrganizationProductRepository organizationProductRepository;

    @Override
    public PurchaseRequestDTO createPurchaseRequest(Long organizationId, PurchaseRequestDTO purchaseRequestDTO) {
        User requestedBy = userRepository.findById(purchaseRequestDTO.getRequestedById())
                .orElseThrow(() -> new ResourceNotFoundException(
                        "User not found with id: " + purchaseRequestDTO.getRequestedById()));
        Organization organization = organizationRepository.findById(organizationId)
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Organization not found with id: " + organizationId));
        ProductCategory productCategory = productCategoryRepository.findById(purchaseRequestDTO.getProductCategoryId())
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Product Category not found with id: " + purchaseRequestDTO.getProductCategoryId()));
        List<PurchaseRequestItem> purchaseRequestItems = purchaseRequestDTO.getPurchaseRequestItems().stream()
                .map(this::mapToPurchaseRequestItem).toList();
        PurchaseRequest purchaseRequest = PurchaseRequest.builder()
                .requestedBy(requestedBy)
                .organization(organization)
                .requestStatus(purchaseRequestDTO.getRequestStatus() != null ? purchaseRequestDTO.getRequestStatus() : RequestStatus.PENDING)
                .requestType(purchaseRequestDTO.getRequestType())
                .procurementSource(purchaseRequestDTO.getProcurementSource())
                .requestDate(Instant.now())
                .expectedDeliveryDate(purchaseRequestDTO.getExpectedDeliveryDate())
                .productCategory(productCategory)
                .purchaseRequestItems(purchaseRequestItems)
                .build();
        purchaseRequestItems.forEach(item -> item.setPurchaseRequest(purchaseRequest));

        purchaseRequestRepository.save(purchaseRequest);
        purchaseRequestItemRepository.saveAll(purchaseRequestItems);

        OrganizationForms organizationForms = organizationFormsRepository
                .findByOrganizationIdAndName(organizationId, "Purchase Request").orElseThrow();

        workflowService.initiateWorkflow(organizationId, organizationForms.getId(), purchaseRequest.getId(), requestedBy.getId());

        return mapToPurchaseRequestDTO(purchaseRequest);
    }

    @Override
    public PurchaseRequestDTO getPurchaseRequestById(Long organizationId, Long id) {
        PurchaseRequest purchaseRequest = purchaseRequestRepository.findByIdAndOrganizationId(id, organizationId)
                .orElseThrow(() -> new ResourceNotFoundException("PurchaseRequest not found with id: " + id));
        return mapToPurchaseRequestDTO(purchaseRequest);
    }

    @Override
    public void deletePurchaseRequest(Long organizationId, Long id) {
        // Implement here
    }

    @Override
    public List<PurchaseRequestDTO> getPurchaseRequestsByRequestedById(Long organizationId, Long requestedById) {
        List<PurchaseRequest> purchaseRequests = purchaseRequestRepository.findAllByRequestedByIdAndOrganizationId(requestedById, organizationId);
        return purchaseRequests.stream()
                .map(this::mapToPurchaseRequestDTO)
                .toList();
    }

    @Override
    public List<PurchaseRequestDTO> getPurchaseRequestsByOrganizationId(Long organizationId) {
        List<PurchaseRequest> purchaseRequests = purchaseRequestRepository.findAllByOrganizationId(organizationId);
        return purchaseRequests.stream()
                .map(this::mapToPurchaseRequestDTO)
                .toList();
    }

    @Override
    public void approvePurchaseRequest(Long organizationId, Long id) {
        PurchaseRequest purchaseRequest = purchaseRequestRepository.findByIdAndOrganizationId(id, organizationId)
                .orElseThrow(() -> new ResourceNotFoundException("PurchaseRequest not found with id: " + id));
        purchaseRequest.setRequestStatus(RequestStatus.APPROVED);
        purchaseRequestRepository.save(purchaseRequest);
    }

    @Override
    public void rejectPurchaseRequest(Long organizationId, Long id) {
        PurchaseRequest purchaseRequest = purchaseRequestRepository.findByIdAndOrganizationId(id, organizationId)
                .orElseThrow(() -> new ResourceNotFoundException("PurchaseRequest not found with id: " + id));
        purchaseRequest.setRequestStatus(RequestStatus.REJECTED);
        purchaseRequestRepository.save(purchaseRequest);
    }

    private PurchaseRequestItemDTO mapToPurchaseRequestItemDTO(PurchaseRequestItem purchaseRequestItem) {
        return PurchaseRequestItemDTO.builder()
                .id(purchaseRequestItem.getId())
                .purchaseRequestId(purchaseRequestItem.getPurchaseRequest().getId())
                .productId(Optional.ofNullable(purchaseRequestItem.getProduct()).map(Product::getId).orElse(null))
                .organizationProductId(Optional.ofNullable(purchaseRequestItem.getOrganizationProduct()).map(OrganizationProduct::getId).orElse(null))
                .quantity(purchaseRequestItem.getQuantity())
                .onlineUrl(purchaseRequestItem.getOnlineUrl())
                .build();
    }

    private PurchaseRequestItem mapToPurchaseRequestItem(PurchaseRequestItemDTO purchaseRequestItemDTO) {
        PurchaseRequestItem.PurchaseRequestItemBuilder builder = PurchaseRequestItem.builder()
                .quantity(purchaseRequestItemDTO.getQuantity())
                .onlineUrl(purchaseRequestItemDTO.getOnlineUrl());

        if (purchaseRequestItemDTO.hasProductId()) {
            Product product = productRepository.findById(purchaseRequestItemDTO.getProductId())
                    .orElseThrow(() -> new RuntimeException("Product not found"));
            builder.product(product);
        } else if (purchaseRequestItemDTO.hasOrganizationProductId()) {
            OrganizationProduct organizationProduct = organizationProductRepository
                    .findById(purchaseRequestItemDTO.getOrganizationProductId())
                    .orElseThrow(() -> new RuntimeException("OrganizationProduct not found"));
            builder.organizationProduct(organizationProduct);
        }
        return builder.build();
    }

    private PurchaseRequestDTO mapToPurchaseRequestDTO(PurchaseRequest purchaseRequest) {
        List<PurchaseRequestItemDTO> purchaseRequestItems = purchaseRequest.getPurchaseRequestItems().stream()
                .map(this::mapToPurchaseRequestItemDTO)
                .toList();

        return PurchaseRequestDTO.builder()
                .id(purchaseRequest.getId())
                .requestedById(purchaseRequest.getRequestedBy().getId())
                .organizationId(purchaseRequest.getOrganization().getId())
                .requestStatus(purchaseRequest.getRequestStatus())
                .requestType(purchaseRequest.getRequestType())
                .procurementSource(purchaseRequest.getProcurementSource())
                .productCategoryId(purchaseRequest.getProductCategory().getId())
                .requestDate(purchaseRequest.getRequestDate())
                .expectedDeliveryDate(purchaseRequest.getExpectedDeliveryDate())
                .purchaseRequestItems(purchaseRequestItems)
                .build();
    }
}
