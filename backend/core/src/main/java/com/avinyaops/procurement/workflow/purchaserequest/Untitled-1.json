{
  "requestedById": 45287810596864,
  "organizationId": 40928446087168,
  "requestStatus": "PENDING",
  "requestType": "BUY",
  "procurementSource": "RFQ",
  "description": "making a purchase request for org items maan",
  "requestDate": "2025-06-27T13:05:37.538Z",
  "expectedDeliveryDate": "2025-06-27T13:05:37.538Z",
  "productCategoryId": 48699778850816,
  "purchaseRequestItems": [
    {
      "productId": 48717886849024,
      "quantity": 70
    },
    {
      "organizationProductId": 50227377401856,
      "quantity": 70
    }

  ]
}



  // Prefill suggestions if field.value is set (for editing)
  // This ensures the AutoComplete displays the selected user when editing
  useEffect(() => {
    let isMounted = true;
    const fetchSelectedUser = async () => {
      if (control?._formValues?.[name]) {
        const userId = control._formValues[name];
        if (userId && typeof userId === 'number') {
          try {
            const user = await UserService.getUserById(userId, organizationId);
            const userOption: UserOption = {
              label: `${user.name} (${user.email})`,
              value: user.id,
              email: user.email
            };
            if (isMounted) {
              setSuggestions([userOption]);
              setInputValue(userOption.label);
            }
          } catch (error) {
            // ignore error
          }
        }
      }
    };
    fetchSelectedUser();
    return () => { isMounted = false; };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [control, name, organizationId]);
