<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to Avinya Ops</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
</head>
<body style="margin: 0; padding: 0; background-color: #f4f4f4; font-family: Arial, sans-serif; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;">
    <!-- Wrapper table for email client compatibility -->
    <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #f4f4f4;">
        <tr>
            <td align="center" style="padding: 20px 0;">
                <!-- Main container -->
                <table cellpadding="0" cellspacing="0" border="0" width="600" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 600px;">
                    
                    <!-- Header -->
                    <tr>
                        <td style="background-color: #00CBA9; padding: 40px 30px; text-align: center; border-radius: 8px 8px 0 0;">
                            <h1 style="color: #ffffff; font-size: 28px; font-weight: bold; margin: 0;">
                                Avinya Ops
                            </h1>
                        </td>
                    </tr>
                    
                    <!-- Main content -->
                    <tr>
                        <td style="padding: 40px 30px;">
                            <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 20px 0;">
                                Dear <strong><span th:text="${name}" style="color: #00CBA9;">User</span></strong>,
                            </p>
                            
                            <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 0 0 25px 0;">
                                Welcome to Avinya Ops! We're thrilled to have you join our community and excited to help you get started on your journey with us.
                            </p>
                            
                            <!-- Account details card -->
                            <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #f8f9fa; border-radius: 8px; border-left: 4px solid #00CBA9; margin: 25px 0;">
                                <tr>
                                    <td style="padding: 25px;">
                                        <h3 style="color: #333333; font-size: 18px; margin: 0 0 15px 0; font-weight: bold;">
                                            Your Account Details
                                        </h3>
                                        
                                        <table cellpadding="0" cellspacing="0" border="0" width="100%">
                                            <tr>
                                                <td style="padding: 8px 0; border-bottom: 1px solid #e9ecef;">
                                                    <strong style="color: #495057; font-size: 14px;">Email:</strong>
                                                </td>
                                                <td style="padding: 8px 0; border-bottom: 1px solid #e9ecef; text-align: right;">
                                                    <span th:text="${email}" style="color: #00CBA9; font-size: 14px; font-weight: 500;"><EMAIL></span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="padding: 8px 0;">
                                                    <strong style="color: #495057; font-size: 14px;">Password:</strong>
                                                </td>
                                                <td style="padding: 8px 0; text-align: right;">
                                                    <span th:text="${password}" style="color: #dc3545; font-family: monospace; font-size: 14px; background-color: #fff3cd; padding: 4px 8px; border-radius: 4px;">password123</span>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </table>
                            
                            <!-- Security notice -->
                            <!-- <table cellpadding="0" cellspacing="0" border="0" width="100%" style="background-color: #fff3cd; border-radius: 6px; border: 1px solid #ffeaa7; margin: 20px 0;">
                                <tr>
                                    <td style="padding: 15px;">
                                        <p style="margin: 0; color: #856404; font-size: 14px; line-height: 1.5;">
                                            <strong>🔒 Security Notice:</strong> Please change your password after your first login for enhanced security.
                                        </p>
                                    </td>
                                </tr>
                            </table>
                             -->
                            
                            <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 25px 0 0 0;">
                                If you have any questions or need assistance getting started, our support team is here to help. Feel free to reach out to us anytime.
                            </p>
                            
                            <p style="color: #333333; font-size: 16px; line-height: 1.6; margin: 25px 0 0 0;">
                                Best regards,<br>
                                <strong style="color: #00CBA9;">The Avinya Ops Team</strong>
                            </p>
                        </td>
                    </tr>
                    
                    <!-- Footer -->
                    <tr>
                        <td style="background-color: #f8f9fa; padding: 30px; text-align: center; border-radius: 0 0 8px 8px; border-top: 1px solid #e9ecef;">
                            <p style="margin: 0 0 10px 0; color: #6c757d; font-size: 12px; line-height: 1.5;">
                                This is an automated message, please do not reply to this email.
                            </p>
                            <p style="margin: 0; color: #6c757d; font-size: 12px; line-height: 1.5;">
                                &copy; 2025 Avinya Ops. All rights reserved.
                            </p>
                            
                            <!-- Social links (optional) -->
                            <!-- <table cellpadding="0" cellspacing="0" border="0" style="margin: 15px auto 0 auto;">
                                <tr>
                                    <td style="padding: 0 5px;">
                                        <a href="#" style="color: #6c757d; font-size: 12px; text-decoration: none;">Privacy Policy</a>
                                    </td>
                                    <td style="color: #6c757d; font-size: 12px;">|</td>
                                    <td style="padding: 0 5px;">
                                        <a href="#" style="color: #6c757d; font-size: 12px; text-decoration: none;">Terms of Service</a>
                                    </td>
                                    <td style="color: #6c757d; font-size: 12px;">|</td>
                                    <td style="padding: 0 5px;">
                                        <a href="#" style="color: #6c757d; font-size: 12px; text-decoration: none;">Contact Support</a>
                                    </td>
                                </tr>
                            </table> -->
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>
</html>